// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'profile_event.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ProfileEvent {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is ProfileEvent);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'ProfileEvent()';
  }
}

/// @nodoc
class $ProfileEventCopyWith<$Res> {
  $ProfileEventCopyWith(ProfileEvent _, $Res Function(ProfileEvent) __);
}

/// Adds pattern-matching-related methods to [ProfileEvent].
extension ProfileEventPatterns on ProfileEvent {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadUserData value)? loadUserData,
    TResult Function(_Logout value)? logout,
    TResult Function(LoadAddressCount value)? loadAddressCount,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _LoadUserData() when loadUserData != null:
        return loadUserData(_that);
      case _Logout() when logout != null:
        return logout(_that);
      case LoadAddressCount() when loadAddressCount != null:
        return loadAddressCount(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadUserData value) loadUserData,
    required TResult Function(_Logout value) logout,
    required TResult Function(LoadAddressCount value) loadAddressCount,
  }) {
    final _that = this;
    switch (_that) {
      case _LoadUserData():
        return loadUserData(_that);
      case _Logout():
        return logout(_that);
      case LoadAddressCount():
        return loadAddressCount(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadUserData value)? loadUserData,
    TResult? Function(_Logout value)? logout,
    TResult? Function(LoadAddressCount value)? loadAddressCount,
  }) {
    final _that = this;
    switch (_that) {
      case _LoadUserData() when loadUserData != null:
        return loadUserData(_that);
      case _Logout() when logout != null:
        return logout(_that);
      case LoadAddressCount() when loadAddressCount != null:
        return loadAddressCount(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadUserData,
    TResult Function()? logout,
    TResult Function()? loadAddressCount,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _LoadUserData() when loadUserData != null:
        return loadUserData();
      case _Logout() when logout != null:
        return logout();
      case LoadAddressCount() when loadAddressCount != null:
        return loadAddressCount();
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadUserData,
    required TResult Function() logout,
    required TResult Function() loadAddressCount,
  }) {
    final _that = this;
    switch (_that) {
      case _LoadUserData():
        return loadUserData();
      case _Logout():
        return logout();
      case LoadAddressCount():
        return loadAddressCount();
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadUserData,
    TResult? Function()? logout,
    TResult? Function()? loadAddressCount,
  }) {
    final _that = this;
    switch (_that) {
      case _LoadUserData() when loadUserData != null:
        return loadUserData();
      case _Logout() when logout != null:
        return logout();
      case LoadAddressCount() when loadAddressCount != null:
        return loadAddressCount();
      case _:
        return null;
    }
  }
}

/// @nodoc

class _LoadUserData implements ProfileEvent {
  const _LoadUserData();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _LoadUserData);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'ProfileEvent.loadUserData()';
  }
}

/// @nodoc

class _Logout implements ProfileEvent {
  const _Logout();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _Logout);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'ProfileEvent.logout()';
  }
}

/// @nodoc

class LoadAddressCount implements ProfileEvent {
  const LoadAddressCount();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is LoadAddressCount);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'ProfileEvent.loadAddressCount()';
  }
}

// dart format on
