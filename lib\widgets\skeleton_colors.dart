import 'package:flutter/material.dart';
import 'package:rozana/core/themes/color_schemes.dart';

/// Colors for skeleton loaders
class SkeletonColors {
  /// Base color for skeleton loaders - more visible, non-glossy
  static Color baseColor() => AppColors.textHint.withValues(alpha: 0.25);

  /// Highlight color for skeleton loaders - more visible, non-glossy
  static Color highlightColor() => AppColors.textHint.withValues(alpha: 0.15);

  /// Border color for skeleton loaders - more visible
  static Color borderColor() => AppColors.textHint.withValues(alpha: 0.2);

  /// Shadow color for skeleton loaders - more visible
  static Color shadowColor() => AppColors.shadowGrey.withValues(alpha: 0.15);

  /// Timeline dot color for skeleton loaders - very light
  static Color timelineDotColor() => AppColors.textHint.withValues(alpha: 0.25);

  /// Timeline connector color for skeleton loaders - more visible
  static Color timelineConnectorColor() =>
      AppColors.textHint.withValues(alpha: 0.3);

  /// Background color for skeleton loaders - more visible, non-glossy
  static Color backgroundColor() => AppColors.background;
}
