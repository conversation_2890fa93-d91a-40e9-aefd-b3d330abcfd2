import 'dart:convert';
import 'package:flutter/widgets.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:rozana/core/dependency_injection/di_container.dart';
import 'package:rozana/core/services/app_preferences_service.dart';
import 'package:rozana/core/utils/app_dimensions.dart';
import 'package:rozana/core/utils/notifier.dart';
import 'package:rozana/data/models/cart_item_model.dart';
import 'package:rozana/data/models/cart_model.dart';

import 'package:rozana/domain/usecases/create_order_usecase.dart';
import 'package:rozana/features/location/services/adress_services.dart';
import 'package:rozana/routes/app_router.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:razorpay_flutter/razorpay_flutter.dart';
import '../services/razorpay_service.dart';

import '../../../core/services/appflyer_services/appflyer_events.dart';
import '../../search/services/typesense_service.dart';
import 'cart_event.dart';
import 'cart_state.dart';

class CartBloc extends Bloc<CartEvent, CartState> {
  final AddressService _addressService;
  final CreateOrderUseCase _createOrderUseCase;
  RazorpayService? _razorpayService;

  CartBloc(
      {required CreateOrderUseCase createOrderUseCase,
      required AddressService addressService})
      : _createOrderUseCase = createOrderUseCase,
        _addressService = addressService,
        super(CartState.initial()) {
    on<CartInit>(_onInit);
    on<CartAddItem>(_onAddItem);
    on<CartRemoveItem>(_onRemoveItem);
    on<CartUpdateQuantity>(_onUpdateQuantity);
    on<CartClear>(_onClear);
    on<CartApplyCoupon>(_onApplyCoupon);
    on<CartRemoveCoupon>(_onRemoveCoupon);
    on<CartImportData>(_onImportData);
    on<CartLoadDefaultAddress>(_onLoadDefaultAddress);
    on<CartSelectAddress>(_onSelectAddress);
    on<CartClearAddress>(_onClearAddress);
    on<CartPlaceOrder>(_onPlaceOrder);
    on<CartCompleteOrder>(_onCompleteOrder);
    on<CartPaymentFailed>(_onPaymentFailed);

    // Initialize the cart
    add(const CartEvent.init());
  }

  Future<void> _onInit(CartInit event, Emitter<CartState> emit) async {
    emit(state.copyWith(isLoading: true));
    try {
      String? cartJson = AppPreferences.getCartData();
      if (cartJson != null) {
        Map<String, dynamic> data = jsonDecode(cartJson);
        emit(state.copyWith(cart: CartModel.fromJson(data)));
      }
    } catch (e) {
      emit(state.copyWith(error: 'Failed to load cart'));
    }
    emit(state.copyWith(isLoading: false));
  }

  Future<void> _onAddItem(CartAddItem event, Emitter<CartState> emit) async {
    CartModel cart = state.cart;
    int availableQuantity = (event.item.availableQuantity ?? 10).toInt();
    int maxQuantityLimit = (event.item.maxQuantity ?? 10).toInt();
    if (availableQuantity < 1) {
      getIt<AppNotifier>().showSnackBar('Item out of stock',
          margin: (event.screen == RouteNames.products)
              ? EdgeInsets.only(
                  bottom: 70,
                  left: AppDimensions.screenHzPadding,
                  right: AppDimensions.screenHzPadding)
              : null);
      return;
    }
    List<CartItemModel> items = [...(cart.items ?? [])];
    final existingIndex = items.indexWhere((i) =>
        ((i.productId == event.item.productId) ||
            (i.skuID == event.item.skuID)));

    if ((maxQuantityLimit < 1)) {
      getIt<AppNotifier>().showSnackBar('Max limit exceeded.',
          margin: (event.screen == RouteNames.products)
              ? EdgeInsets.only(
                  bottom: 70,
                  left: AppDimensions.screenHzPadding,
                  right: AppDimensions.screenHzPadding)
              : null);
      return;
    }

    if (existingIndex != -1) {
      if ((availableQuantity <= (items[existingIndex].quantity ?? 0)) ||
          (maxQuantityLimit <= ((items[existingIndex].quantity ?? 0)))) {
        getIt<AppNotifier>().showSnackBar('Max limit exceeded.',
            margin: (event.screen == RouteNames.products)
                ? EdgeInsets.only(
                    bottom: 70,
                    left: AppDimensions.screenHzPadding,
                    right: AppDimensions.screenHzPadding)
                : null);
        return;
      }
      CartItemModel updatedItem = items[existingIndex].copyWith(
        quantity:
            (items[existingIndex].quantity ?? 0) + (event.item.quantity ?? 0),
      );
      items[existingIndex] = updatedItem;
    } else {
      items.add(CartItemModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        productId: event.item.productId,
        name: event.item.name,
        price: event.item.price,
        imageUrl: event.item.imageUrl,
        quantity: event.item.quantity,
        unit: event.item.unit,
        discountedPrice: event.item.discountedPrice,
        customizations: event.item.customizations,
        facilityId: event.item.facilityId,
        facilityName: event.item.facilityName,
        skuID: event.item.skuID,
        availableQuantity: availableQuantity,
        maxQuantity: maxQuantityLimit,
      ));
    }

    CartModel updatedCart = cart.copyWith(items: items).recalculate();
    await _saveToStorage(updatedCart);
    emit(state.copyWith(cart: updatedCart));

    // Log add to cart event to AppsFlyer
    await AppsFlyerEvents.addToCart(
      sku: event.item.skuID,
      productName: event.item.name,
      price: event.item.price?.toDouble(),
    );
  }

  Future<void> _onRemoveItem(
      CartRemoveItem event, Emitter<CartState> emit) async {
    CartItemModel? removingItem = state.cart.items
        ?.firstWhere((i) => ((i.id == event.itemId) || (i.skuID == event.sku)));
    final updatedCart = state.cart
        .copyWith(
          items: state.cart.items?.where((i) => i.id != event.itemId).toList(),
        )
        .recalculate();

    await _saveToStorage(updatedCart);
    emit(state.copyWith(cart: updatedCart));

    // Log remove from cart event to AppsFlyer
    await AppsFlyerEvents.removeFromCart(
      sku: removingItem?.skuID,
      productName: removingItem?.name,
      price: removingItem?.price?.toDouble(),
    );
  }

  Future<void> _onUpdateQuantity(
      CartUpdateQuantity event, Emitter<CartState> emit) async {
    final items = state.cart.items?.map((item) {
      if ((item.id == event.itemId) || (item.skuID == event.sku)) {
        return item.copyWith(quantity: event.quantity);
      }
      return item;
    }).toList();

    CartItemModel? item = state.cart.items
        ?.firstWhere((i) => ((i.id == event.itemId) || (i.skuID == event.sku)));

    int availableQuantity = (item?.availableQuantity ?? 0).toInt();
    int maxQuantityLimit = (item?.maxQuantity ?? 0).toInt();

    bool isAdded = (items
                ?.firstWhere(
                    (i) => ((i.id == event.itemId) || (i.skuID == event.sku)))
                .quantity ??
            0) >=
        (item?.quantity ?? 0);

    if (isAdded) {
      if ((availableQuantity <= (item?.quantity ?? 0)) ||
          (maxQuantityLimit <= ((item?.quantity ?? 0)))) {
        getIt<AppNotifier>().showSnackBar('Max limit exceeded.',
            margin: (event.screen == RouteNames.products)
                ? EdgeInsets.only(
                    bottom: 70,
                    left: AppDimensions.screenHzPadding,
                    right: AppDimensions.screenHzPadding)
                : null);
        return;
      }
    }

    final updatedCart = state.cart.copyWith(items: items).recalculate();
    await _saveToStorage(updatedCart);
    emit(state.copyWith(cart: updatedCart));

    if (isAdded) {
      // Log add to cart event to AppsFlyer
      await AppsFlyerEvents.addToCart(
        sku: item?.skuID,
        productName: item?.name,
        price: item?.price?.toDouble(),
      );
    } else {
      // Log remove from cart event to AppsFlyer
      await AppsFlyerEvents.removeFromCart(
        sku: item?.skuID,
        productName: item?.name,
        price: item?.price?.toDouble(),
      );
    }
  }

  Future<void> _onClear(CartClear event, Emitter<CartState> emit) async {
    final clearedCart = CartModel();

    emit(state.copyWith(cart: clearedCart));
    await _saveToStorage(clearedCart);
  }

  Future<void> _onApplyCoupon(
      CartApplyCoupon event, Emitter<CartState> emit) async {
    CartModel cart = state.cart;
    num discount = 0;

    num subTotal = cart.subTotal ?? 0;

    switch (event.code.toUpperCase()) {
      case 'WELCOME10':
        discount = subTotal * 0.1;
        break;
      case 'FLAT50':
        discount = subTotal < 50 ? subTotal : 50;
        break;
      case 'FREESHIP':
        emit(state.copyWith(cart: cart.copyWith(deliveryFee: 0)));
        return;
      case 'SUMMER20':
        discount = subTotal * 0.2;
        break;
      default:
        emit(state.copyWith(error: 'Invalid coupon code'));
        return;
    }

    final updatedCart = cart.copyWith(discount: discount).recalculate();
    await _saveToStorage(updatedCart);
    emit(state.copyWith(cart: updatedCart, appliedCoupon: event.code));
  }

  Future<void> _onRemoveCoupon(
      CartRemoveCoupon event, Emitter<CartState> emit) async {
    num resetFee =
        state.appliedCoupon == 'FREESHIP' ? 40 : state.cart.deliveryFee ?? 0;
    final updatedCart =
        state.cart.copyWith(discount: 0, deliveryFee: resetFee).recalculate();
    await _saveToStorage(updatedCart);
    emit(state.copyWith(cart: updatedCart, appliedCoupon: null));
  }

  Future<void> _onImportData(
      CartImportData event, Emitter<CartState> emit) async {
    try {
      final data = jsonDecode(event.jsonData);
      final importedCart = CartModel.fromJson(data);
      await _saveToStorage(importedCart);
      emit(state.copyWith(cart: importedCart));
    } catch (_) {
      emit(state.copyWith(error: 'Invalid cart data'));
    }
  }

  Future<void> _saveToStorage(CartModel cart) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('cart_data', jsonEncode(cart.toJson()));
  }

  // Address related event handlers
  Future<void> _onLoadDefaultAddress(
      CartLoadDefaultAddress event, Emitter<CartState> emit) async {
    emit(state.copyWith(isLoadingAddress: true));
    try {
      final typesenseService = GetIt.instance<TypesenseService>();
      // First try to get cached selected address (fast)
      final cachedAddress = _addressService.getCurrentSelectedAddressSync();

      if (cachedAddress != null) {
        final store = await typesenseService.findStoreByCoordinates(
            cachedAddress.latitude?.toDouble() ?? 0,
            cachedAddress.longitude?.toDouble() ?? 0);
        if (store != null) {
          emit(state.copyWith(
            deliveryAddress: cachedAddress,
            isLoadingAddress: false,
          ));
        } else {
          emit(state.copyWith(
            deliveryAddress: null,
            isLoadingAddress: false,
          ));
        }

        return;
      }

      // If no cached address, do fresh detection
      final selectedAddress = await _addressService.getSelectedAddress();
      final store = await typesenseService.findStoreByCoordinates(
          selectedAddress?.latitude?.toDouble() ?? 0,
          selectedAddress?.longitude?.toDouble() ?? 0);
      if (store != null) {
        emit(state.copyWith(
          deliveryAddress: selectedAddress,
          isLoadingAddress: false,
        ));
      } else {
        emit(state.copyWith(
          deliveryAddress: null,
          isLoadingAddress: false,
        ));
      }
    } catch (e) {
      emit(state.copyWith(
        isLoadingAddress: false,
        error: 'Failed to load selected address',
      ));
    }
  }

  Future<void> _onSelectAddress(
      CartSelectAddress event, Emitter<CartState> emit) async {
    // Update the AddressService with manually selected address
    _addressService.setSelectedAddress(event.address);
    emit(state.copyWith(deliveryAddress: event.address));
  }

  Future<void> _onClearAddress(
      CartClearAddress event, Emitter<CartState> emit) async {
    emit(state.copyWith(deliveryAddress: null));
  }

  Future<void> _onPlaceOrder(
      CartPlaceOrder event, Emitter<CartState> emit) async {
    // Check if cart is empty
    if (state.cart.items?.isEmpty ?? true) return;

    // Emit processing state
    emit(state.copyWith(orderStatus: OrderProcessingStatus.processing));

    try {
      // Create order first for all payment methods
      await _createOrderWithUseCase(event.paymentMethod, emit);
    } catch (e) {
      // Emit error state
      emit(state.copyWith(
        orderStatus: OrderProcessingStatus.error,
        error: 'Failed to create order: $e',
      ));
    }
  }

  /// Reset cart state - useful for user logout scenarios
  void resetCart() {
    add(const CartEvent.clear());
  }

  @override
  Future<void> close() {
    // Dispose Razorpay service if it exists
    _razorpayService?.dispose();
    return super.close();
  }

  // Handle order completion event
  void _onCompleteOrder(CartCompleteOrder event, Emitter<CartState> emit) {
    // Clear cart after order completion (success or failure)
    add(CartEvent.clear());

    // Emit success state with order data (includes payment status)
    emit(state.copyWith(
      orderStatus: OrderProcessingStatus.success,
      orderId: event.orderId,
      orderData: event.orderData,
    ));
  }

  // Handle payment failed event
  void _onPaymentFailed(CartPaymentFailed event, Emitter<CartState> emit) {
    emit(state.copyWith(
      orderStatus: OrderProcessingStatus.error,
      error: event.errorMessage,
    ));
  }

  /// Create order using the CreateOrderUseCase
  Future<void> _createOrderWithUseCase(
    String paymentMethod,
    Emitter<CartState> emit, {
    Map<String, dynamic>? paymentDetails,
  }) async {
    try {
      // Create order parameters
      final params = CreateOrderParams(
        items: state.cart.items ?? [],
        paymentMethod: paymentMethod,
        deliveryAddress: state.deliveryAddress,
        baseTotal: (state.cart.subTotal ?? 0).toDouble(),
        deliveryFee: (state.cart.deliveryFee ?? 0).toDouble(),
        tax: (state.cart.tax ?? 0).toDouble(),
        discount: (state.cart.discount ?? 0).toDouble(),
        paymentDetails: paymentDetails,
      );

      // Call the use case
      final result = await _createOrderUseCase(params);

      if (result.isSuccess) {
        if (result.requiresPayment && result.razorpayOrderId != null) {
          // For Razorpay orders, initiate payment using the razorpay_order_id
          await _initiateRazorpayPayment(result, emit);
        } else if (result.requiresPayment && result.razorpayOrderId == null) {
          // Razorpay order but no razorpay_order_id - this is an error
          emit(state.copyWith(
            orderStatus: OrderProcessingStatus.error,
            error: 'Payment order ID not found. Please try again.',
          ));
        } else {
          // For COD orders, complete immediately
          add(CartEvent.completeOrder(
            orderId: result.orderId!,
            orderData: result.orderData!,
          ));
        }
      } else {
        // Emit error state
        emit(state.copyWith(
          orderStatus: OrderProcessingStatus.error,
          error: result.error ?? 'Failed to create order',
        ));
      }
    } catch (e) {
      // Emit error state
      emit(state.copyWith(
        orderStatus: OrderProcessingStatus.error,
        error: 'Failed to create order: $e',
      ));
    }
  }

  /// Initiate Razorpay payment using the order details from backend
  Future<void> _initiateRazorpayPayment(
    CreateOrderResult orderResult,
    Emitter<CartState> emit,
  ) async {
    try {
      // Emit pending payment state
      emit(state.copyWith(orderStatus: OrderProcessingStatus.pendingPayment));

      // Get customer details from the order result or user data
      final customerDetails = orderResult.customerDetails;
      String name = customerDetails?['customer_name'] ?? 'Customer';
      String email =
          customerDetails?['customer_email'] ?? '<EMAIL>';
      String contact = customerDetails?['customer_phone'] ?? '9999999999';

      // Initialize Razorpay service with callbacks
      _razorpayService = RazorpayService(
        onPaymentSuccess: (PaymentSuccessResponse response) {
          // On payment success, dispatch complete order event
          add(CartEvent.completeOrder(
            orderId: orderResult.orderId!,
            orderData: orderResult.orderData!,
          ));
        },
        onPaymentError: (PaymentFailureResponse response) {
          // On payment error, navigate to order success screen with failure status
          add(CartEvent.completeOrder(
            orderId: orderResult.orderId!,
            orderData: {
              ...?orderResult.orderData,
              'paymentStatus': 'failed',
              'paymentError': response.message ?? 'Unknown error',
              'canRetryPayment': true,
              'razorpayOrderId': orderResult.razorpayOrderId,
            },
          ));
        },
        onExternalWallet: (ExternalWalletResponse response) {
          debugPrint('External wallet: ${response.walletName}');
        },
      );

      // Process payment using the razorpay_order_id from backend
      _razorpayService?.processPayment(
        razorpayOrderId: orderResult.razorpayOrderId!,
        name: name,
        email: email,
        contact: contact,
        description: 'Order ${orderResult.orderId} from Rozana',
      );
    } catch (e) {
      emit(state.copyWith(
        orderStatus: OrderProcessingStatus.error,
        error: 'Failed to initiate payment: $e',
      ));
    }
  }
}
