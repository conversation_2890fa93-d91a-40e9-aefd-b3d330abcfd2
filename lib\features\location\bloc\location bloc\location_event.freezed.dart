// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'location_event.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$LocationEvent {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is LocationEvent);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'LocationEvent()';
  }
}

/// @nodoc
class $LocationEventCopyWith<$Res> {
  $LocationEventCopyWith(LocationEvent _, $Res Function(LocationEvent) __);
}

/// Adds pattern-matching-related methods to [LocationEvent].
extension LocationEventPatterns on LocationEvent {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_RefreshLocation value)? refreshLocation,
    TResult Function(_RequestPermissionAndDetect value)?
        requestPermissionAndDetect,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _Started() when started != null:
        return started(_that);
      case _RefreshLocation() when refreshLocation != null:
        return refreshLocation(_that);
      case _RequestPermissionAndDetect()
          when requestPermissionAndDetect != null:
        return requestPermissionAndDetect(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_RefreshLocation value) refreshLocation,
    required TResult Function(_RequestPermissionAndDetect value)
        requestPermissionAndDetect,
  }) {
    final _that = this;
    switch (_that) {
      case _Started():
        return started(_that);
      case _RefreshLocation():
        return refreshLocation(_that);
      case _RequestPermissionAndDetect():
        return requestPermissionAndDetect(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_RefreshLocation value)? refreshLocation,
    TResult? Function(_RequestPermissionAndDetect value)?
        requestPermissionAndDetect,
  }) {
    final _that = this;
    switch (_that) {
      case _Started() when started != null:
        return started(_that);
      case _RefreshLocation() when refreshLocation != null:
        return refreshLocation(_that);
      case _RequestPermissionAndDetect()
          when requestPermissionAndDetect != null:
        return requestPermissionAndDetect(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function(AddressModel? tempAddress)? refreshLocation,
    TResult Function()? requestPermissionAndDetect,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _Started() when started != null:
        return started();
      case _RefreshLocation() when refreshLocation != null:
        return refreshLocation(_that.tempAddress);
      case _RequestPermissionAndDetect()
          when requestPermissionAndDetect != null:
        return requestPermissionAndDetect();
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function(AddressModel? tempAddress) refreshLocation,
    required TResult Function() requestPermissionAndDetect,
  }) {
    final _that = this;
    switch (_that) {
      case _Started():
        return started();
      case _RefreshLocation():
        return refreshLocation(_that.tempAddress);
      case _RequestPermissionAndDetect():
        return requestPermissionAndDetect();
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function(AddressModel? tempAddress)? refreshLocation,
    TResult? Function()? requestPermissionAndDetect,
  }) {
    final _that = this;
    switch (_that) {
      case _Started() when started != null:
        return started();
      case _RefreshLocation() when refreshLocation != null:
        return refreshLocation(_that.tempAddress);
      case _RequestPermissionAndDetect()
          when requestPermissionAndDetect != null:
        return requestPermissionAndDetect();
      case _:
        return null;
    }
  }
}

/// @nodoc

class _Started implements LocationEvent {
  const _Started();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _Started);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'LocationEvent.started()';
  }
}

/// @nodoc

class _RefreshLocation implements LocationEvent {
  const _RefreshLocation({this.tempAddress});

  final AddressModel? tempAddress;

  /// Create a copy of LocationEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$RefreshLocationCopyWith<_RefreshLocation> get copyWith =>
      __$RefreshLocationCopyWithImpl<_RefreshLocation>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _RefreshLocation &&
            (identical(other.tempAddress, tempAddress) ||
                other.tempAddress == tempAddress));
  }

  @override
  int get hashCode => Object.hash(runtimeType, tempAddress);

  @override
  String toString() {
    return 'LocationEvent.refreshLocation(tempAddress: $tempAddress)';
  }
}

/// @nodoc
abstract mixin class _$RefreshLocationCopyWith<$Res>
    implements $LocationEventCopyWith<$Res> {
  factory _$RefreshLocationCopyWith(
          _RefreshLocation value, $Res Function(_RefreshLocation) _then) =
      __$RefreshLocationCopyWithImpl;
  @useResult
  $Res call({AddressModel? tempAddress});
}

/// @nodoc
class __$RefreshLocationCopyWithImpl<$Res>
    implements _$RefreshLocationCopyWith<$Res> {
  __$RefreshLocationCopyWithImpl(this._self, this._then);

  final _RefreshLocation _self;
  final $Res Function(_RefreshLocation) _then;

  /// Create a copy of LocationEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? tempAddress = freezed,
  }) {
    return _then(_RefreshLocation(
      tempAddress: freezed == tempAddress
          ? _self.tempAddress
          : tempAddress // ignore: cast_nullable_to_non_nullable
              as AddressModel?,
    ));
  }
}

/// @nodoc

class _RequestPermissionAndDetect implements LocationEvent {
  const _RequestPermissionAndDetect();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _RequestPermissionAndDetect);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'LocationEvent.requestPermissionAndDetect()';
  }
}

// dart format on
