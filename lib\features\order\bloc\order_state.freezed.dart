// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'order_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$OrderState {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is OrderState);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'OrderState()';
  }
}

/// @nodoc
class $OrderStateCopyWith<$Res> {
  $OrderStateCopyWith(OrderState _, $Res Function(OrderState) __);
}

/// Adds pattern-matching-related methods to [OrderState].
extension OrderStatePatterns on OrderState {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(OrderInitial value)? initial,
    TResult Function(OrderLoading value)? loading,
    TResult Function(OrderHistoryLoaded value)? orderHistoryLoaded,
    TResult Function(OrderDetailsLoaded value)? orderDetailsLoaded,
    TResult Function(OrderCancelled value)? orderCancelled,
    TResult Function(OrderError value)? error,
    TResult Function(OrderEmpty value)? empty,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case OrderInitial() when initial != null:
        return initial(_that);
      case OrderLoading() when loading != null:
        return loading(_that);
      case OrderHistoryLoaded() when orderHistoryLoaded != null:
        return orderHistoryLoaded(_that);
      case OrderDetailsLoaded() when orderDetailsLoaded != null:
        return orderDetailsLoaded(_that);
      case OrderCancelled() when orderCancelled != null:
        return orderCancelled(_that);
      case OrderError() when error != null:
        return error(_that);
      case OrderEmpty() when empty != null:
        return empty(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(OrderInitial value) initial,
    required TResult Function(OrderLoading value) loading,
    required TResult Function(OrderHistoryLoaded value) orderHistoryLoaded,
    required TResult Function(OrderDetailsLoaded value) orderDetailsLoaded,
    required TResult Function(OrderCancelled value) orderCancelled,
    required TResult Function(OrderError value) error,
    required TResult Function(OrderEmpty value) empty,
  }) {
    final _that = this;
    switch (_that) {
      case OrderInitial():
        return initial(_that);
      case OrderLoading():
        return loading(_that);
      case OrderHistoryLoaded():
        return orderHistoryLoaded(_that);
      case OrderDetailsLoaded():
        return orderDetailsLoaded(_that);
      case OrderCancelled():
        return orderCancelled(_that);
      case OrderError():
        return error(_that);
      case OrderEmpty():
        return empty(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(OrderInitial value)? initial,
    TResult? Function(OrderLoading value)? loading,
    TResult? Function(OrderHistoryLoaded value)? orderHistoryLoaded,
    TResult? Function(OrderDetailsLoaded value)? orderDetailsLoaded,
    TResult? Function(OrderCancelled value)? orderCancelled,
    TResult? Function(OrderError value)? error,
    TResult? Function(OrderEmpty value)? empty,
  }) {
    final _that = this;
    switch (_that) {
      case OrderInitial() when initial != null:
        return initial(_that);
      case OrderLoading() when loading != null:
        return loading(_that);
      case OrderHistoryLoaded() when orderHistoryLoaded != null:
        return orderHistoryLoaded(_that);
      case OrderDetailsLoaded() when orderDetailsLoaded != null:
        return orderDetailsLoaded(_that);
      case OrderCancelled() when orderCancelled != null:
        return orderCancelled(_that);
      case OrderError() when error != null:
        return error(_that);
      case OrderEmpty() when empty != null:
        return empty(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(
            List<OrderEntity> orders,
            bool isLoadingMore,
            bool hasMoreData,
            int currentPage,
            String currentFilter,
            String searchQuery)?
        orderHistoryLoaded,
    TResult Function(OrderEntity order)? orderDetailsLoaded,
    TResult Function(String orderId, String message)? orderCancelled,
    TResult Function(String message, String? orderId)? error,
    TResult Function(String filter, String searchQuery)? empty,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case OrderInitial() when initial != null:
        return initial();
      case OrderLoading() when loading != null:
        return loading();
      case OrderHistoryLoaded() when orderHistoryLoaded != null:
        return orderHistoryLoaded(
            _that.orders,
            _that.isLoadingMore,
            _that.hasMoreData,
            _that.currentPage,
            _that.currentFilter,
            _that.searchQuery);
      case OrderDetailsLoaded() when orderDetailsLoaded != null:
        return orderDetailsLoaded(_that.order);
      case OrderCancelled() when orderCancelled != null:
        return orderCancelled(_that.orderId, _that.message);
      case OrderError() when error != null:
        return error(_that.message, _that.orderId);
      case OrderEmpty() when empty != null:
        return empty(_that.filter, _that.searchQuery);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(
            List<OrderEntity> orders,
            bool isLoadingMore,
            bool hasMoreData,
            int currentPage,
            String currentFilter,
            String searchQuery)
        orderHistoryLoaded,
    required TResult Function(OrderEntity order) orderDetailsLoaded,
    required TResult Function(String orderId, String message) orderCancelled,
    required TResult Function(String message, String? orderId) error,
    required TResult Function(String filter, String searchQuery) empty,
  }) {
    final _that = this;
    switch (_that) {
      case OrderInitial():
        return initial();
      case OrderLoading():
        return loading();
      case OrderHistoryLoaded():
        return orderHistoryLoaded(
            _that.orders,
            _that.isLoadingMore,
            _that.hasMoreData,
            _that.currentPage,
            _that.currentFilter,
            _that.searchQuery);
      case OrderDetailsLoaded():
        return orderDetailsLoaded(_that.order);
      case OrderCancelled():
        return orderCancelled(_that.orderId, _that.message);
      case OrderError():
        return error(_that.message, _that.orderId);
      case OrderEmpty():
        return empty(_that.filter, _that.searchQuery);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(
            List<OrderEntity> orders,
            bool isLoadingMore,
            bool hasMoreData,
            int currentPage,
            String currentFilter,
            String searchQuery)?
        orderHistoryLoaded,
    TResult? Function(OrderEntity order)? orderDetailsLoaded,
    TResult? Function(String orderId, String message)? orderCancelled,
    TResult? Function(String message, String? orderId)? error,
    TResult? Function(String filter, String searchQuery)? empty,
  }) {
    final _that = this;
    switch (_that) {
      case OrderInitial() when initial != null:
        return initial();
      case OrderLoading() when loading != null:
        return loading();
      case OrderHistoryLoaded() when orderHistoryLoaded != null:
        return orderHistoryLoaded(
            _that.orders,
            _that.isLoadingMore,
            _that.hasMoreData,
            _that.currentPage,
            _that.currentFilter,
            _that.searchQuery);
      case OrderDetailsLoaded() when orderDetailsLoaded != null:
        return orderDetailsLoaded(_that.order);
      case OrderCancelled() when orderCancelled != null:
        return orderCancelled(_that.orderId, _that.message);
      case OrderError() when error != null:
        return error(_that.message, _that.orderId);
      case OrderEmpty() when empty != null:
        return empty(_that.filter, _that.searchQuery);
      case _:
        return null;
    }
  }
}

/// @nodoc

class OrderInitial implements OrderState {
  const OrderInitial();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is OrderInitial);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'OrderState.initial()';
  }
}

/// @nodoc

class OrderLoading implements OrderState {
  const OrderLoading();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is OrderLoading);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'OrderState.loading()';
  }
}

/// @nodoc

class OrderHistoryLoaded implements OrderState {
  const OrderHistoryLoaded(
      {required final List<OrderEntity> orders,
      this.isLoadingMore = false,
      this.hasMoreData = true,
      this.currentPage = 0,
      this.currentFilter = '',
      this.searchQuery = ''})
      : _orders = orders;

  final List<OrderEntity> _orders;
  List<OrderEntity> get orders {
    if (_orders is EqualUnmodifiableListView) return _orders;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_orders);
  }

  @JsonKey()
  final bool isLoadingMore;
  @JsonKey()
  final bool hasMoreData;
  @JsonKey()
  final int currentPage;
  @JsonKey()
  final String currentFilter;
  @JsonKey()
  final String searchQuery;

  /// Create a copy of OrderState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $OrderHistoryLoadedCopyWith<OrderHistoryLoaded> get copyWith =>
      _$OrderHistoryLoadedCopyWithImpl<OrderHistoryLoaded>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is OrderHistoryLoaded &&
            const DeepCollectionEquality().equals(other._orders, _orders) &&
            (identical(other.isLoadingMore, isLoadingMore) ||
                other.isLoadingMore == isLoadingMore) &&
            (identical(other.hasMoreData, hasMoreData) ||
                other.hasMoreData == hasMoreData) &&
            (identical(other.currentPage, currentPage) ||
                other.currentPage == currentPage) &&
            (identical(other.currentFilter, currentFilter) ||
                other.currentFilter == currentFilter) &&
            (identical(other.searchQuery, searchQuery) ||
                other.searchQuery == searchQuery));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_orders),
      isLoadingMore,
      hasMoreData,
      currentPage,
      currentFilter,
      searchQuery);

  @override
  String toString() {
    return 'OrderState.orderHistoryLoaded(orders: $orders, isLoadingMore: $isLoadingMore, hasMoreData: $hasMoreData, currentPage: $currentPage, currentFilter: $currentFilter, searchQuery: $searchQuery)';
  }
}

/// @nodoc
abstract mixin class $OrderHistoryLoadedCopyWith<$Res>
    implements $OrderStateCopyWith<$Res> {
  factory $OrderHistoryLoadedCopyWith(
          OrderHistoryLoaded value, $Res Function(OrderHistoryLoaded) _then) =
      _$OrderHistoryLoadedCopyWithImpl;
  @useResult
  $Res call(
      {List<OrderEntity> orders,
      bool isLoadingMore,
      bool hasMoreData,
      int currentPage,
      String currentFilter,
      String searchQuery});
}

/// @nodoc
class _$OrderHistoryLoadedCopyWithImpl<$Res>
    implements $OrderHistoryLoadedCopyWith<$Res> {
  _$OrderHistoryLoadedCopyWithImpl(this._self, this._then);

  final OrderHistoryLoaded _self;
  final $Res Function(OrderHistoryLoaded) _then;

  /// Create a copy of OrderState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? orders = null,
    Object? isLoadingMore = null,
    Object? hasMoreData = null,
    Object? currentPage = null,
    Object? currentFilter = null,
    Object? searchQuery = null,
  }) {
    return _then(OrderHistoryLoaded(
      orders: null == orders
          ? _self._orders
          : orders // ignore: cast_nullable_to_non_nullable
              as List<OrderEntity>,
      isLoadingMore: null == isLoadingMore
          ? _self.isLoadingMore
          : isLoadingMore // ignore: cast_nullable_to_non_nullable
              as bool,
      hasMoreData: null == hasMoreData
          ? _self.hasMoreData
          : hasMoreData // ignore: cast_nullable_to_non_nullable
              as bool,
      currentPage: null == currentPage
          ? _self.currentPage
          : currentPage // ignore: cast_nullable_to_non_nullable
              as int,
      currentFilter: null == currentFilter
          ? _self.currentFilter
          : currentFilter // ignore: cast_nullable_to_non_nullable
              as String,
      searchQuery: null == searchQuery
          ? _self.searchQuery
          : searchQuery // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class OrderDetailsLoaded implements OrderState {
  const OrderDetailsLoaded({required this.order});

  final OrderEntity order;

  /// Create a copy of OrderState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $OrderDetailsLoadedCopyWith<OrderDetailsLoaded> get copyWith =>
      _$OrderDetailsLoadedCopyWithImpl<OrderDetailsLoaded>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is OrderDetailsLoaded &&
            (identical(other.order, order) || other.order == order));
  }

  @override
  int get hashCode => Object.hash(runtimeType, order);

  @override
  String toString() {
    return 'OrderState.orderDetailsLoaded(order: $order)';
  }
}

/// @nodoc
abstract mixin class $OrderDetailsLoadedCopyWith<$Res>
    implements $OrderStateCopyWith<$Res> {
  factory $OrderDetailsLoadedCopyWith(
          OrderDetailsLoaded value, $Res Function(OrderDetailsLoaded) _then) =
      _$OrderDetailsLoadedCopyWithImpl;
  @useResult
  $Res call({OrderEntity order});
}

/// @nodoc
class _$OrderDetailsLoadedCopyWithImpl<$Res>
    implements $OrderDetailsLoadedCopyWith<$Res> {
  _$OrderDetailsLoadedCopyWithImpl(this._self, this._then);

  final OrderDetailsLoaded _self;
  final $Res Function(OrderDetailsLoaded) _then;

  /// Create a copy of OrderState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? order = null,
  }) {
    return _then(OrderDetailsLoaded(
      order: null == order
          ? _self.order
          : order // ignore: cast_nullable_to_non_nullable
              as OrderEntity,
    ));
  }
}

/// @nodoc

class OrderCancelled implements OrderState {
  const OrderCancelled({required this.orderId, required this.message});

  final String orderId;
  final String message;

  /// Create a copy of OrderState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $OrderCancelledCopyWith<OrderCancelled> get copyWith =>
      _$OrderCancelledCopyWithImpl<OrderCancelled>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is OrderCancelled &&
            (identical(other.orderId, orderId) || other.orderId == orderId) &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, orderId, message);

  @override
  String toString() {
    return 'OrderState.orderCancelled(orderId: $orderId, message: $message)';
  }
}

/// @nodoc
abstract mixin class $OrderCancelledCopyWith<$Res>
    implements $OrderStateCopyWith<$Res> {
  factory $OrderCancelledCopyWith(
          OrderCancelled value, $Res Function(OrderCancelled) _then) =
      _$OrderCancelledCopyWithImpl;
  @useResult
  $Res call({String orderId, String message});
}

/// @nodoc
class _$OrderCancelledCopyWithImpl<$Res>
    implements $OrderCancelledCopyWith<$Res> {
  _$OrderCancelledCopyWithImpl(this._self, this._then);

  final OrderCancelled _self;
  final $Res Function(OrderCancelled) _then;

  /// Create a copy of OrderState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? orderId = null,
    Object? message = null,
  }) {
    return _then(OrderCancelled(
      orderId: null == orderId
          ? _self.orderId
          : orderId // ignore: cast_nullable_to_non_nullable
              as String,
      message: null == message
          ? _self.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class OrderError implements OrderState {
  const OrderError({required this.message, this.orderId});

  final String message;
  final String? orderId;

  /// Create a copy of OrderState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $OrderErrorCopyWith<OrderError> get copyWith =>
      _$OrderErrorCopyWithImpl<OrderError>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is OrderError &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.orderId, orderId) || other.orderId == orderId));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message, orderId);

  @override
  String toString() {
    return 'OrderState.error(message: $message, orderId: $orderId)';
  }
}

/// @nodoc
abstract mixin class $OrderErrorCopyWith<$Res>
    implements $OrderStateCopyWith<$Res> {
  factory $OrderErrorCopyWith(
          OrderError value, $Res Function(OrderError) _then) =
      _$OrderErrorCopyWithImpl;
  @useResult
  $Res call({String message, String? orderId});
}

/// @nodoc
class _$OrderErrorCopyWithImpl<$Res> implements $OrderErrorCopyWith<$Res> {
  _$OrderErrorCopyWithImpl(this._self, this._then);

  final OrderError _self;
  final $Res Function(OrderError) _then;

  /// Create a copy of OrderState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? message = null,
    Object? orderId = freezed,
  }) {
    return _then(OrderError(
      message: null == message
          ? _self.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      orderId: freezed == orderId
          ? _self.orderId
          : orderId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class OrderEmpty implements OrderState {
  const OrderEmpty({this.filter = '', this.searchQuery = ''});

  @JsonKey()
  final String filter;
  @JsonKey()
  final String searchQuery;

  /// Create a copy of OrderState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $OrderEmptyCopyWith<OrderEmpty> get copyWith =>
      _$OrderEmptyCopyWithImpl<OrderEmpty>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is OrderEmpty &&
            (identical(other.filter, filter) || other.filter == filter) &&
            (identical(other.searchQuery, searchQuery) ||
                other.searchQuery == searchQuery));
  }

  @override
  int get hashCode => Object.hash(runtimeType, filter, searchQuery);

  @override
  String toString() {
    return 'OrderState.empty(filter: $filter, searchQuery: $searchQuery)';
  }
}

/// @nodoc
abstract mixin class $OrderEmptyCopyWith<$Res>
    implements $OrderStateCopyWith<$Res> {
  factory $OrderEmptyCopyWith(
          OrderEmpty value, $Res Function(OrderEmpty) _then) =
      _$OrderEmptyCopyWithImpl;
  @useResult
  $Res call({String filter, String searchQuery});
}

/// @nodoc
class _$OrderEmptyCopyWithImpl<$Res> implements $OrderEmptyCopyWith<$Res> {
  _$OrderEmptyCopyWithImpl(this._self, this._then);

  final OrderEmpty _self;
  final $Res Function(OrderEmpty) _then;

  /// Create a copy of OrderState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? filter = null,
    Object? searchQuery = null,
  }) {
    return _then(OrderEmpty(
      filter: null == filter
          ? _self.filter
          : filter // ignore: cast_nullable_to_non_nullable
              as String,
      searchQuery: null == searchQuery
          ? _self.searchQuery
          : searchQuery // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

// dart format on
