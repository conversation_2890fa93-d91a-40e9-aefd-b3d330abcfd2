import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rozana/features/categories/presentation/widgets/categoy_card.dart';

import '../../../../core/utils/app_dimensions.dart';
import '../../../../domain/entities/category_entity.dart';
import '../../bloc/home bloc/home_bloc.dart';

class TopCategoriesSection extends StatelessWidget {
  const TopCategoriesSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 15),
      child: BlocBuilder<HomeBloc, HomeState>(
        buildWhen: (previous, current) {
          List<CategoryEntity> prevCategories =
              previous.mapOrNull(loaded: (value) => value.categories) ?? [];

          List<CategoryEntity> currCategories =
              current.mapOrNull(loaded: (value) => value.categories) ?? [];

          return prevCategories != currCategories;
        },
        builder: (context, state) {
          List<CategoryEntity> categories = state.maybeMap(
              loaded: (value) => value.categories ?? [], orElse: () => []);
          return GridView.builder(
            primary: false,
            shrinkWrap: true,
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 3,
              childAspectRatio: 1,
              mainAxisSpacing: 16,
              crossAxisSpacing: 16,
            ),
            padding:
                EdgeInsets.symmetric(horizontal: AppDimensions.screenHzPadding),
            itemCount: categories.length > 6 ? 6 : categories.length,
            itemBuilder: (ctx, index) {
              return CategoryOfferCard(category: categories[index]);
            },
          );
        },
      ),
    );
  }
}
