import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:rozana/core/themes/color_schemes.dart';
import 'package:rozana/core/utils/helpers.dart';
import 'package:rozana/widgets/custom_image.dart';
import 'package:rozana/widgets/custom_text.dart';

import '../../../../core/services/appflyer_services/appflyer_events.dart';
import '../../../../core/utils/color_utils.dart';
import '../../../../domain/entities/category_entity.dart';
import '../../../../routes/app_router.dart';
import '../../../home/<USER>/home bloc/home_bloc.dart';
import '../../bloc/categories_bloc.dart';

class CategoryOfferCard extends StatelessWidget {
  final CategoryEntity? category;
  final CategoryEntity? subCategory;

  const CategoryOfferCard({
    super.key,
    required this.category,
    this.subCategory,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () async {
        HapticFeedback.lightImpact();
        Map<String, dynamic> extras = {};
        if (subCategory != null) {
          extras['category'] = subCategory;
          extras['parent-category'] = category;
        } else if (category != null) {
          extras['category'] = category;
        }
        context.push(RouteNames.products, extra: extras);
        await AppsFlyerEvents.categoryView(
          category?.parentID ?? '',
          category?.name ?? '',
        );
      },
      child: Stack(
        alignment: Alignment.topCenter,
        children: [
          Container(
            width: double.infinity,
            decoration: BoxDecoration(
              color: Color(0xFF5C469C),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Stack(
              alignment: Alignment.topLeft,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    const SizedBox(height: 20),
                    Spacer(),
                    Align(
                      alignment: Alignment.bottomRight,
                      child: CustomImage(
                        imageUrl: category?.imageUrl,
                        width: MediaQuery.of(context).size.width * 0.14,
                      ),
                    ),
                  ],
                ),
                Padding(
                  padding: const EdgeInsets.fromLTRB(12, 22, 12, 0),
                  child: CustomText(
                    category?.name ?? 'NA',
                    fontWeight: FontWeight.w500,
                    fontSize: 12,
                    maxLines: 2,
                    color: AppColors.white,
                    textAlign: TextAlign.start,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            decoration: BoxDecoration(
              color: const Color(0xFFFFDA63), // Match the banner color
              borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(6),
                  bottomRight: Radius.circular(6)),
            ),
            child: FittedBox(
              child: CustomText(
                '₹50 OFF',
                fontWeight: FontWeight.w600,
                fontSize: 11,
                color: AppColors.textPrimary,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class CategoryCard extends StatelessWidget {
  const CategoryCard({
    super.key,
    this.category,
    this.width,
    this.height,
    this.radius,
    this.imagePadding,
    this.subCategory,
    this.fontSize = 12,
    this.onTap,
    this.textPadding,
    this.color,
  });
  final CategoryEntity? category;
  final CategoryEntity? subCategory;

  final double? width;
  final double? height;
  final double? radius;
  final EdgeInsetsGeometry? imagePadding;
  final EdgeInsetsGeometry? textPadding;
  final double fontSize;
  final void Function()? onTap;
  final Color? color;

  @override
  Widget build(BuildContext context) {
    CategoryEntity? currentCategory = subCategory ?? category;
    return GestureDetector(
      onTap: onTap ??
          () async {
            HapticFeedback.lightImpact();
            CategoriesBloc.loadedSubcategories.clear();
            Map<String, dynamic> extras = {};
            if (subCategory != null) {
              extras['category'] = subCategory;
              extras['parent-category'] = category;
            } else if (category != null) {
              extras['category'] = category;
            }
            context.push(RouteNames.products, extra: extras);
            await AppsFlyerEvents.categoryView(
              category?.parentID ?? '',
              category?.name ?? '',
            );
          },
      child: Container(
        height: height,
        width: width,
        decoration: BoxDecoration(
          color: color ??
              ColorUtils.generateConsistentColor(currentCategory?.id ?? '',
                  baseOpacity: 0.2),
          borderRadius: BorderRadiusGeometry.circular(radius ?? 20),
        ),
        child: Column(
          children: [
            Expanded(
                child: Padding(
              padding: imagePadding ?? EdgeInsets.fromLTRB(8, 8, 8, 4),
              child: ClipRRect(
                borderRadius: BorderRadiusGeometry.circular(radius ?? 14),
                child: CustomImage(
                  imageUrl: currentCategory?.imageUrl,
                  width: double.infinity,
                ),
              ),
            )),
            ConstrainedBox(
              constraints: BoxConstraints(minHeight: 33),
              child: Padding(
                padding: textPadding ?? EdgeInsets.fromLTRB(6, 0, 6, 6),
                child: Center(
                  child: CustomText(
                    currentCategory?.name ?? '',
                    fontSize: fontSize,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF24292E),
                    maxLines: 2,
                    textAlign: TextAlign.center,
                    textHeight: 1.2,
                  ),
                ),
              ),
            )
          ],
        ),
      ),
      // Column(
      //   mainAxisSize: MainAxisSize.min,
      //   children: [
      //     Container(
      //       width: width ?? 100,
      //       height: subCategoryTheme ? null : (height ?? 100),
      //       decoration: BoxDecoration(
      //           color: ColorUtils.generateConsistentColor(
      //               currentCategory?.id ?? '',
      //               baseOpacity: 0.2),
      //           borderRadius: BorderRadius.circular(radius ?? 20)),
      //       child: ClipRRect(
      //           borderRadius: BorderRadiusGeometry.circular(radius ?? 20),
      //           child: Padding(
      //             padding: imagePadding ?? EdgeInsets.zero,
      //             child: Column(
      //               children: [
      //                 ConstrainedBox(
      //                   constraints: BoxConstraints(
      //                       maxHeight: (height ?? 100) -
      //                           (imagePadding?.vertical ?? 0),
      //                       minWidth: (width ?? 100) -
      //                           (imagePadding?.horizontal ?? 0)),
      //                   child: CustomImage(
      //                     imageUrl: currentCategory?.imageUrl,
      //                     height: ((height != null) && subCategoryTheme)
      //                         ? (height! - 50)
      //                         : null,
      //                   ),
      //                 ),
      //                 Visibility(
      //                   visible: subCategoryTheme,
      //                   child: ConstrainedBox(
      //                     constraints: BoxConstraints(minHeight: 33),
      //                     child: Padding(
      //                       padding: const EdgeInsets.only(top: 5),
      //                       child: Center(
      //                         child: CustomText(
      //                           currentCategory?.name ?? '',
      //                           fontSize: fontSize,
      //                           fontWeight: FontWeight.w600,
      //                           color: Color(0xFF24292E),
      //                           maxLines: 2,
      //                           textAlign: TextAlign.center,
      //                           textHeight: 1.2,
      //                         ),
      //                       ),
      //                     ),
      //                   ),
      //                 ),
      //               ],
      //             ),
      //           )),
      //     ),
      //     Visibility(
      //       visible: !subCategoryTheme,
      //       child: Padding(
      //         padding: textPadding ?? const EdgeInsets.only(top: 5),
      //         child: SizedBox(
      //           width: textWidth,
      //           child: CustomText(
      //             currentCategory?.name ?? '',
      //             fontSize: fontSize,
      //             fontWeight: FontWeight.w600,
      //             color: Color(0xFF24292E),
      //             maxLines: 2,
      //             textAlign: TextAlign.center,
      //             textHeight: 1.2,
      //           ),
      //         ),
      //       ),
      //     ),
      //   ],
      // )
    );
  }
}

class AppBarCategoryCard extends StatelessWidget {
  const AppBarCategoryCard({
    super.key,
    this.category,
    this.fontSize = 12,
    this.onTap,
    required this.isSelected,
    required this.index,
    this.imageColor,
  });
  final CategoryEntity? category;

  final double fontSize;
  final void Function()? onTap;
  final bool isSelected;
  final int index;
  final Color? imageColor;

  @override
  Widget build(BuildContext context) {
    CategoryEntity? currentCategory = category;

    return InkWell(
        onTap: () {
          HapticFeedback.lightImpact();
          Scrollable.ensureVisible(
              (HomeBloc.categoryKeys ?? [])[index].currentContext!,
              alignment: 0.5,
              duration: Duration(milliseconds: 500));
          context
              .read<HomeBloc>()
              .add(HomeEvent.switchCategory(currentCategory, index));
        },
        child: ConstrainedBox(
          constraints: BoxConstraints(minWidth: 65, maxWidth: 65),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              CustomImage(
                imageUrl: currentCategory?.imageUrl,
                height: 35,
                width: 35,
                imageColor: imageColor,
              ),
              SizedBox(height: 6),
              FittedBox(
                child: CustomText(
                  TextFormatter.getFormattedCategoryText(
                    currentCategory?.name ?? '',
                  ),
                  fontSize: fontSize,
                  fontWeight: FontWeight.w500,
                  color: AppColors.white,
                  maxLines: 2,
                  textAlign: TextAlign.center,
                  textHeight: 1.2,
                ),
              ),
              SizedBox(height: 5),
              Container(
                height: 3,
                decoration: BoxDecoration(
                    color: isSelected ? Colors.white : Colors.transparent,
                    borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(10),
                        topRight: Radius.circular(10))),
              )
            ],
          ),
        ));
  }
}
