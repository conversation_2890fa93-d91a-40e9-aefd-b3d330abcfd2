class NewProductEntity {
  final String brandId;
  final String brandName;
  final String categoryId;
  final String categoryName;
  final String description;
  final String name;
  final List<String> photos;
  final int totalCount;
  final List<VariantEntity> variants;

  NewProductEntity({
    required this.brandId,
    required this.brandName,
    required this.categoryId,
    required this.categoryName,
    required this.description,
    required this.name,
    required this.photos,
    required this.totalCount,
    required this.variants,
  });

  @override
  String toString() {
    return 'NewProductEntity(brandId: $brandId, brandName: $brandName, categoryId: $categoryId, categoryName: $categoryName, description: $description, name: $name, photos: $photos, totalCount: $totalCount, variants: $variants)';
  }
}

class VariantEntity {
  final String variantName;
  final dynamic availableQty;
  final dynamic maxPurchaseLimit;
  final dynamic mrp;
  final String id;
  final dynamic sellingPrice;

  VariantEntity({
    required this.variantName,
    required this.availableQty,
    required this.maxPurchaseLimit,
    required this.mrp,
    required this.id,
    required this.sellingPrice,
  });

  @override
  String toString() {
    return 'VariantEntity(variantName: $variantName, availableQty: $availableQty, maxPurchaseLimit: $maxPurchaseLimit, mrp: $mrp, id: $id, sellingPrice: $sellingPrice)';
  }
}
