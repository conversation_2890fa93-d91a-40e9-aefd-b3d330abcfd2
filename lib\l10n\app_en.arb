{"@@locale": "en", "appTitle": "<PERSON><PERSON><PERSON>", "@appTitle": {"description": "The application title"}, "myCart": "My Cart", "@myCart": {"description": "Cart screen title"}, "placeOrder": "Place Order", "@placeOrder": {"description": "Place order button text"}, "selectAddress": "Select Address", "@selectAddress": {"description": "Select address button text"}, "loginToProceed": "Login to Proceed", "@loginToProceed": {"description": "Login prompt text"}, "myProfile": "My Profile", "@myProfile": {"description": "Profile screen title"}, "yourOrders": "Your Orders", "@yourOrders": {"description": "Orders menu item"}, "helpSupport": "Help & Support", "@helpSupport": {"description": "Help and support menu item"}, "logout": "Logout", "@logout": {"description": "Logout button text"}, "retry": "Retry", "@retry": {"description": "Retry button text"}, "loading": "Loading...", "@loading": {"description": "Loading indicator text"}, "error": "Error", "@error": {"description": "Generic error text"}, "somethingWentWrong": "Something went wrong", "@somethingWentWrong": {"description": "Generic error message"}, "welcomeToRozana": "Welcome to Rozana", "@welcomeToRozana": {"description": "Welcome message on login screen"}, "verifyOtp": "Verify OTP", "@verifyOtp": {"description": "OTP verification screen title"}, "total": "Total", "@total": {"description": "Total amount label"}, "totalItems": "Total ({count} items)", "@totalItems": {"description": "Total items count", "placeholders": {"count": {"type": "int", "example": "3"}}}, "savedAddresses": "Saved Addresses", "@savedAddresses": {"description": "Saved addresses menu item"}, "address": "Address", "@address": {"description": "Single address label"}, "addresses": "Addresses", "@addresses": {"description": "Multiple addresses label"}, "refunds": "Refunds", "@refunds": {"description": "Refunds menu item"}, "profile": "Profile", "@profile": {"description": "Profile menu item"}, "rewards": "Rewards", "@rewards": {"description": "Rewards menu item"}, "paymentManagement": "Payment Management", "@paymentManagement": {"description": "Payment management menu item"}, "wishlist": "Wishlist", "@wishlist": {"description": "Wishlist menu item"}, "notifications": "Notifications", "@notifications": {"description": "Notifications menu item"}, "rozanaPay": "Rozana <PERSON>", "@rozanaPay": {"description": "Rozana Pay feature name"}, "language": "Language", "@language": {"description": "Language selection label"}, "english": "English", "@english": {"description": "English language option"}, "hindi": "हिंदी", "@hindi": {"description": "Hindi language option"}, "clearCart": "Clear Cart", "@clearCart": {"description": "Clear cart action"}, "confirmClearCart": "Are you sure you want to clear your cart?", "@confirmClearCart": {"description": "Clear cart confirmation message"}, "cancel": "Cancel", "@cancel": {"description": "Cancel button text"}, "clear": "Clear", "@clear": {"description": "Clear button text"}, "save": "Save", "@save": {"description": "Save button text"}, "ok": "OK", "@ok": {"description": "OK button text"}, "yes": "Yes", "@yes": {"description": "Yes button text"}, "no": "No", "@no": {"description": "No button text"}, "home": "Home", "@home": {"description": "Home address type"}, "work": "Work", "@work": {"description": "Work address type"}, "other": "Other", "@other": {"description": "Other address type"}, "addToCart": "Add to Cart", "@addToCart": {"description": "Add to cart button"}, "buyNow": "Buy Now", "@buyNow": {"description": "Buy now button"}, "outOfStock": "Out of Stock", "@outOfStock": {"description": "Out of stock label"}, "login": "<PERSON><PERSON>", "@login": {"description": "Login button text"}, "signUp": "Sign Up", "@signUp": {"description": "Sign up button text"}, "otpVerification": "OTP Verification", "@otpVerification": {"description": "OTP verification title"}, "addAddress": "Add Address", "@addAddress": {"description": "Add address button"}, "editAddress": "Edit Address", "@editAddress": {"description": "Edit address button"}, "deleteAddress": "Delete Address", "@deleteAddress": {"description": "Delete address button"}, "defaultAddress": "<PERSON><PERSON><PERSON> Address", "@defaultAddress": {"description": "Default address label"}, "setAsDefault": "Set as <PERSON><PERSON><PERSON>", "@setAsDefault": {"description": "Set as default address button"}, "deliveryAddress": "Delivery Address", "@deliveryAddress": {"description": "Delivery address section title"}, "paymentMethod": "Payment Method", "@paymentMethod": {"description": "Payment method section title"}, "cashOnDelivery": "Cash on Delivery", "@cashOnDelivery": {"description": "Cash on delivery payment option"}, "orderSummary": "Order Summary", "@orderSummary": {"description": "Order summary section title"}, "subtotal": "Subtotal", "@subtotal": {"description": "Subtotal label"}, "deliveryFee": "Delivery Fee", "@deliveryFee": {"description": "Delivery fee label"}, "tax": "Tax", "@tax": {"description": "Tax label"}, "discount": "Discount", "@discount": {"description": "Discount label"}, "free": "FREE", "@free": {"description": "Free delivery label"}, "youSaved": "You saved", "@youSaved": {"description": "Savings message prefix"}, "onThisOrder": "on this order", "@onThisOrder": {"description": "Savings message suffix"}, "emptyCart": "Your cart is empty", "@emptyCart": {"description": "Empty cart message"}, "startShopping": "Start Shopping", "@startShopping": {"description": "Start shopping button"}, "remove": "Remove", "@remove": {"description": "Remove item button"}, "quantity": "Quantity", "@quantity": {"description": "Quantity label"}, "price": "Price", "@price": {"description": "Price label"}, "originalPrice": "Original Price", "@originalPrice": {"description": "Original price label"}, "discountedPrice": "Discounted Price", "@discountedPrice": {"description": "Discounted price label"}, "refresh": "Refresh", "@refresh": {"description": "Refresh action"}, "pullToRefresh": "Pull to refresh", "@pullToRefresh": {"description": "Pull to refresh instruction"}, "deleteMyAccount": "Delete My Account", "@deleteMyAccount": {"description": "Delete account option in profile"}, "aboutUs": "About Us", "@aboutUs": {"description": "About us page title"}, "privacyPolicy": "Privacy Policy", "@privacyPolicy": {"description": "Privacy policy page title"}, "termsAndConditions": "Terms and Conditions", "@termsAndConditions": {"description": "Terms and conditions page title"}}