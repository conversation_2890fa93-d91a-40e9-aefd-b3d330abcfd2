import '../config/environment_config.dart';

/// Base URL configuration using environment variables
/// ⚠️ SECURITY: URLs are now loaded from environment configuration
class BaseUrl {
  /// Get the base URL for the current environment (defaults to OMS)
  /// This is now loaded from environment variables for security
  static String get baseUrl => EnvironmentConfig.omsBaseUrl;

  /// Get OMS (Order Management System) base URL
  static String get omsBaseUrl => EnvironmentConfig.omsBaseUrl;

  /// Get IMS (Inventory Management System) base URL
  static String get imsBaseUrl => EnvironmentConfig.imsBaseUrl;

  /// Get base URL for specific service
  /// [isIms] - true for IMS service, false for OMS service
  static String getServiceBaseUrl({required bool isIms}) {
    return EnvironmentConfig.getServiceBaseUrl(isIms: isIms);
  }

  /// Legacy support - deprecated, use baseUrl instead
  @Deprecated('Use baseUrl instead. This will be removed in future versions.')
  static String get localHostUrl => "";

  @Deprecated('Use EnvironmentConfig.omsBaseUrl instead')
  static String get devUrl => EnvironmentConfig.omsBaseUrl;

  @Deprecated('Use EnvironmentConfig.omsBaseUrl instead')
  static String get stagingUrl => EnvironmentConfig.omsBaseUrl;

  @Deprecated('Use EnvironmentConfig.omsBaseUrl instead')
  static String get productionUrl => EnvironmentConfig.omsBaseUrl;
}

class EndUrl {
  // ==================== OMS (Order Management System) Endpoints ====================
  // Auth endpoints (typically OMS)
  static const String loginUser = 'api/sign-in';
  static const String registerUser = 'api/register';
  static const String forgotPassword = 'api/forgot-password';

  // Order endpoints (OMS)
  static const String createOrder = 'app/v1/create_order';
  static const String getOrderHistory = 'app/v1/orders';
  static const String getOrderDetails = 'app/v1/order_details';
  static const String cancelOrder = 'api/orders'; // append /{orderId}/cancel

  // ==================== IMS (Inventory Management System) Endpoints ====================
  // Product/Inventory endpoints (typically IMS)
  static const String getProducts = 'api/products';
  static const String getProductDetails = 'api/products'; // append /{productId}
  static const String getCategories = 'api/categories';
  static const String getSubCategories = 'api/subcategories';
  static const String checkProductAvailability = 'api/products/availability';
  static const String getProductStock = 'api/products/stock';
  static const String searchProducts = 'api/products/search';
  static const String getFeaturedProducts = 'api/products/featured';
  static const String getProductsByCategory =
      'api/products/category'; // append /{categoryId}

  // ==================== Service Type Helpers ====================
  /// Helper method to determine if an endpoint should use IMS
  static bool isImsEndpoint(String endpoint) {
    const imsEndpoints = [
      getProducts,
      getProductDetails,
      getCategories,
      getSubCategories,
      checkProductAvailability,
      getProductStock,
      searchProducts,
      getFeaturedProducts,
      getProductsByCategory,
    ];

    return imsEndpoints.any((imsEndpoint) => endpoint.startsWith(imsEndpoint));
  }
}
