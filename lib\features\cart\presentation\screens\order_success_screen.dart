import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/themes/color_schemes.dart';
import '../../../../routes/app_router.dart';
import '../../../../widgets/custom_button.dart';
import '../widgets/order_summary_widget.dart';

class OrderSuccessScreen extends StatelessWidget {
  final String orderId;
  final Map<String, dynamic> orderData;

  const OrderSuccessScreen({
    super.key,
    required this.orderId,
    required this.orderData,
  });

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvoked: (_) {
        context.go(RouteNames.home);
      },
      child: Scaffold(
        backgroundColor: Colors.white,
        body: SafeArea(
          child: Column(
            children: [
              Expanded(
                child: Center(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(24),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        // Success animation/icon
                        Container(
                          width: 120,
                          height: 120,
                          decoration: BoxDecoration(
                            color: Colors.green.withValues(alpha: 0.1),
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.check_circle,
                            color: Colors.green,
                            size: 80,
                          ),
                        ),
                        const SizedBox(height: 24),

                        // Success message
                        const Text(
                          'Order Placed Successfully!',
                          style: TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: Colors.black87,
                          ),
                          textAlign: TextAlign.center,
                        ),

                        // Payment status message
                        if (_isPaymentFailed())
                          ..._buildPaymentFailedSection(context),
                        const SizedBox(height: 16),

                        // Order ID
                        Text(
                          'Order ID: #$orderId',
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: Colors.black54,
                          ),
                        ),
                        const SizedBox(height: 32),

                        // Order summary
                        OrderSummaryWidget(orderData: orderData),
                        const SizedBox(height: 32),

                        // Estimated delivery
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: AppColors.primary.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Row(
                            children: [
                              const Icon(
                                Icons.access_time,
                                color: AppColors.primary,
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    const Text(
                                      'Estimated Delivery',
                                      style: TextStyle(
                                        fontSize: 14,
                                        color: Colors.black54,
                                      ),
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      _getEstimatedDeliveryTime(),
                                      style: const TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.black87,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 16),

                        // Thank you message
                        const Text(
                          'Thank you for shopping with Rozana!',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: Colors.black87,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                ),
              ),

              // Bottom buttons
              Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    AppButton(
                      text: 'Continue Shopping',
                      onPressed: () => context.go(RouteNames.home),
                      backgroundColor: AppColors.primary,
                      textColor: Colors.white,
                      borderRadius: 8,
                    ),
                    const SizedBox(height: 12),
                    AppButton(
                      text: 'View Order Details',
                      onPressed: () {
                        context.go('${RouteNames.orders}/$orderId',
                            extra: {'fromOrderSuccess': true});
                      },
                      backgroundColor: Colors.white,
                      textColor: AppColors.primary,
                      borderColor: AppColors.primary,
                      borderRadius: 8,
                      isOutlined: true,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _getEstimatedDeliveryTime() {
    // Calculate estimated delivery time (24 hours from now)
    final now = DateTime.now();
    final tomorrow = now.add(const Duration(days: 1));

    // Format the date
    final month = _getMonthName(tomorrow.month);
    final day = tomorrow.day;

    return '$month $day, ${_formatTimeOfDay(tomorrow.hour, tomorrow.minute)}';
  }

  String _getMonthName(int month) {
    const months = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December'
    ];
    return months[month - 1];
  }

  String _formatTimeOfDay(int hour, int minute) {
    final period = hour < 12 ? 'AM' : 'PM';
    final formattedHour = hour > 12 ? hour - 12 : (hour == 0 ? 12 : hour);
    final formattedMinute = minute.toString().padLeft(2, '0');

    return '$formattedHour:$formattedMinute $period';
  }

  /// Check if payment failed
  bool _isPaymentFailed() {
    return orderData['paymentStatus'] == 'failed';
  }

  /// Build payment failed section
  List<Widget> _buildPaymentFailedSection(BuildContext context) {
    return [
      const SizedBox(height: 16),
      Container(
        padding: const EdgeInsets.all(16),
        margin: const EdgeInsets.symmetric(horizontal: 8),
        decoration: BoxDecoration(
          color: AppColors.warning.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: AppColors.warning.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Column(
          children: [
            Row(
              children: [
                Icon(
                  Icons.warning_amber_rounded,
                  color: AppColors.warning,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Payment Failed',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: AppColors.warning,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        orderData['paymentError'] ??
                            'Payment could not be processed',
                        style: const TextStyle(
                          fontSize: 14,
                          color: AppColors.textGrey,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    ];
  }
}
