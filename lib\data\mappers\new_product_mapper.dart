import '../models/new_product_model.dart';
import 'package:rozana/domain/entities/new_product_entity.dart';


class NewProductMapper {
  static NewProductEntity toEntity(NewProductModel model) {
    return NewProductEntity(
      brandId: model.brandId,
      brandName: model.brandName,
      categoryId: model.categoryId,
      categoryName: model.categoryName,
      description: model.description,
      name: model.name,
      photos: List<String>.from(model.photos),
      totalCount: model.totalCount,
      variants: model.variants.map((variant) => VariantEntity(
        variantName: variant.variantName,
        availableQty: variant.availableQty,
        maxPurchaseLimit: variant.maxPurchaseLimit,
        mrp: variant.mrp,
        id: variant.id,
        sellingPrice: variant.sellingPrice,
      )).toList(),
    );
  }

  static NewProductModel toModel(NewProductEntity entity) {
    return NewProductModel(
      brandId: entity.brandId,
      brandName: entity.brandName,
      categoryId: entity.categoryId,
      categoryName: entity.categoryName,
      description: entity.description,
      name: entity.name,
      photos: List<String>.from(entity.photos),
      totalCount: entity.totalCount,
      variants: entity.variants.map((variant) => VariantModel(
        variantName: variant.variantName,
        availableQty: variant.availableQty,
        maxPurchaseLimit: variant.maxPurchaseLimit,
        mrp: variant.mrp,
        id: variant.id,
        sellingPrice: variant.sellingPrice,
      )).toList(),
    );
  }
}
