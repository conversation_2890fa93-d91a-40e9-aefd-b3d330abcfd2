import 'dart:async';
import 'package:flutter/material.dart';
import 'package:rozana/core/themes/color_schemes.dart';
import 'package:rozana/widgets/custom_text.dart';

class AnimatedSearchPlaceholder extends StatefulWidget {
  const AnimatedSearchPlaceholder({super.key});

  @override
  State<AnimatedSearchPlaceholder> createState() =>
      _AnimatedSearchPlaceholderState();
}

class _AnimatedSearchPlaceholderState extends State<AnimatedSearchPlaceholder>
    with TickerProviderStateMixin {
  final List<String> suggestions = [
    "'Milk'",
    "'Biscuit'",
    "'Sugar'",
    "'Electronics'",
    "'Footwear'",
    "'Grocery'",
    "'Snacks'",
    "'Cosmetics'",
    "'Fashion'",
    "'Sports'"
  ];

  int _currentIndex = 0;
  int _nextIndex = 1;
  late Timer _timer;
  late AnimationController _animationController;
  late Animation<double> _fadeOutAnimation;
  late Animation<double> _fadeInAnimation;
  late Animation<Offset> _slideOutAnimation;
  late Animation<Offset> _slideInAnimation;

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    // Create staggered animations for smoother transitions
    _fadeOutAnimation = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.0, 0.4, curve: Curves.easeInOut),
    ));

    _fadeInAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.6, 1.0, curve: Curves.easeInOut),
    ));

    _slideOutAnimation = Tween<Offset>(
      begin: Offset.zero,
      end: const Offset(0.0, -0.5),
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.0, 0.4, curve: Curves.easeInOut),
    ));

    _slideInAnimation = Tween<Offset>(
      begin: const Offset(0.0, 0.5),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.6, 1.0, curve: Curves.easeInOut),
    ));

    _startTimer();
  }

  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 3), (timer) {
      _animateToNext();
    });
  }

  void _animateToNext() {
    _nextIndex = (_currentIndex + 1) % suggestions.length;
    _animationController.forward().then((_) {
      setState(() {
        _currentIndex = _nextIndex;
      });
      _animationController.reset();
    });
  }

  @override
  void dispose() {
    _timer.cancel();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 20, // Fixed height to prevent layout shifts
      child: Stack(
        alignment: Alignment.centerLeft,
        children: [
          // Current text (fading out and sliding up)
          AnimatedBuilder(
            animation: _animationController,
            builder: (context, child) {
              return SlideTransition(
                position: _slideOutAnimation,
                child: FadeTransition(
                  opacity: _fadeOutAnimation,
                  child: CustomText(
                    suggestions[_currentIndex],
                    color: AppColors.textGrey,
                    fontSize: 14,
                    maxLines: 1,
                    fontWeight: FontWeight.w500,
                    overflow: TextOverflow.ellipsis,
                    textHeight: 0.9,
                  ),
                ),
              );
            },
          ),
          // Next text (fading in and sliding up)
          AnimatedBuilder(
            animation: _animationController,
            builder: (context, child) {
              return SlideTransition(
                position: _slideInAnimation,
                child: FadeTransition(
                  opacity: _fadeInAnimation,
                  child: CustomText(
                    suggestions[_nextIndex],
                    color: AppColors.textGrey,
                    fontSize: 14,
                    maxLines: 1,
                    fontWeight: FontWeight.w500,
                    overflow: TextOverflow.ellipsis,
                    textHeight: 0.9,
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}