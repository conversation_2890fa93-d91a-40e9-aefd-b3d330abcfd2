import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:rozana/widgets/custom_image.dart';
import 'package:rozana/widgets/custom_text.dart';
import 'package:rozana/widgets/shimmer_widgets.dart';
import 'package:rozana/widgets/skeleton_loader_factory.dart';

import '../../../../core/services/appflyer_services/appflyer_events.dart';
import '../../../../core/themes/color_schemes.dart';
import '../../../../data/models/cart_item_model.dart';
import '../../../../domain/entities/product_entity.dart';
import '../../../../data/mappers/product_mapper.dart';
import '../../../../routes/app_router.dart';
import '../../../cart/bloc/cart_bloc.dart';
import '../../../cart/bloc/cart_event.dart';
import '../../../cart/bloc/cart_state.dart';
import '../../../cart/utils/cart_utils.dart';

class DiscountedProductCard extends StatelessWidget {
  final ProductEntity? product;
  final bool isLoading;
  final double? height;
  final bool staggeredLoading;
  final int staggerIndex;
  final EdgeInsets? imagePadding;
  final EdgeInsets? textPadding;

  const DiscountedProductCard({
    super.key,
    required this.product,
    required this.isLoading,
    this.height,
    this.staggeredLoading = false,
    this.staggerIndex = 0,
    this.imagePadding,
    this.textPadding,
  });

  @override
  Widget build(BuildContext context) {
  
    return isLoading
        ? staggeredLoading
            ? SkeletonLoaderFactory.createProductSkeleton(
                height: height,
                radius: 10,
              )
            : ShimmerBox()
        : GestureDetector(
            onTap: () async {
              context.push(RouteNames.productDetail, extra: {
                'product': product != null
                    ? ProductMapper.toModel(product!).toJson()
                    : null,
              });
              // Log product view event to AppsFlyer
              await AppsFlyerEvents.productView(
                  sku: product?.skuID,
                  productName: product?.name,
                  price: product?.price,
                  category: product?.category);
            },
            child: Stack(
              alignment: Alignment.topCenter,
              children: [
                Container(
                  margin: EdgeInsets.only(top: 10),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(10.0),
                    border: Border.all(color: Color(0xFFF1F2DA), width: 1),
                  ),
                  child: Column(
                    children: [
                      Expanded(
                        child: Padding(
                          padding: const EdgeInsets.fromLTRB(8, 15, 8, 5),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(10.0),
                            child: CustomImage(
                              imageUrl: product?.imageUrl,
                              width: double.infinity,
                              fit: BoxFit.cover,
                            ),
                          ),
                        ),
                      ),
                      Padding(
                        padding: textPadding ??
                            const EdgeInsets.fromLTRB(8, 0, 8, 0),
                        child: ConstrainedBox(
                          constraints: BoxConstraints(
                            minHeight: 20,
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              CustomText(
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                                product?.name ?? '--',
                                fontSize: 10,
                                fontWeight: FontWeight.w600,
                              ),
                              if (product?.variantName != null && product!.variantName!.isNotEmpty) ...[
                                const SizedBox(height: 2),
                                CustomText(
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                  ' ${product!.variantName}',
                                  fontSize: 8,
                                  color: Colors.grey,
                                  fontWeight: FontWeight.normal,
                                ),
                              ],
                            ],
                          ),
                        ),
                      ),
                      Padding(
                        padding: textPadding ??
                            const EdgeInsets.fromLTRB(8, 8, 8, 8),
                        child: BlocBuilder<CartBloc, CartState>(
                          builder: (context, state) {
                            String productId = product?.id ?? '';
                            final quantity = CartUtils.getItemQuantity(
                                productId, product?.skuID ?? '', state.cart);
                            return quantity > 0
                                ? Container(
                                    height: MediaQuery.of(context).size.width *
                                        0.09,
                                    decoration: BoxDecoration(
                                      color: AppColors
                                          .primary, // Green for Add button
                                      borderRadius: BorderRadius.circular(3.0),
                                    ),
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        // Minus button
                                        InkWell(
                                          splashColor: Colors.transparent,
                                          onTap: () async {
                                            HapticFeedback.lightImpact();
                                            final cartItemId =
                                                CartUtils.getCartItemId(
                                                    productId,
                                                    product?.skuID ?? '',
                                                    state.cart);

                                            if (cartItemId != null) {
                                              if (quantity > 1) {
                                                context.read<CartBloc>().add(
                                                    CartEvent.updateQuantity(
                                                        cartItemId,
                                                        product?.skuID ?? '',
                                                        (quantity - 1)
                                                            .toInt()));
                                              } else if (quantity == 1) {
                                                context.read<CartBloc>().add(
                                                    CartEvent.removeItem(
                                                        cartItemId,
                                                        product?.skuID ?? ''));
                                              }
                                            }
                                          },
                                          child: Padding(
                                            padding: const EdgeInsets.symmetric(
                                                horizontal: 8, vertical: 6),
                                            child: const Icon(
                                              Icons.remove,
                                              color: AppColors.surface,
                                              size: 12,
                                            ),
                                          ),
                                        ),

                                        // Quantity display
                                        AbsorbPointer(
                                          absorbing: true,
                                          child: CustomText(
                                            '$quantity',
                                            color: AppColors.surface,
                                            fontWeight: FontWeight.w600,
                                            fontSize: 12,
                                          ),
                                        ),

                                        // Plus button
                                        InkWell(
                                          splashColor: Colors.transparent,
                                          onTap: () async {
                                            HapticFeedback.lightImpact();
                                            final cartItemId =
                                                CartUtils.getCartItemId(
                                                    productId,
                                                    product?.skuID ?? '',
                                                    state.cart);
                                            if (cartItemId != null) {
                                              String screen =
                                                  GoRouterState.of(context)
                                                      .uri
                                                      .toString();
                                              context
                                                  .read<CartBloc>()
                                                  .add(CartEvent.updateQuantity(
                                                    cartItemId,
                                                    product?.skuID ?? '',
                                                    (quantity + 1).toInt(),
                                                    screen: screen,
                                                  ));
                                            }

                                            // widget.onAddToCart?.call();
                                          },
                                          child: Padding(
                                            padding: const EdgeInsets.symmetric(
                                                horizontal: 8, vertical: 6),
                                            child: const Icon(
                                              Icons.add,
                                              color: AppColors.surface,
                                              size: 16,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ))
                                : InkWell(
                                    onTap: () {
                                      String screen = GoRouterState.of(context)
                                          .uri
                                          .toString();
                                      HapticFeedback.lightImpact();
                                      context
                                          .read<CartBloc>()
                                          .add(CartEvent.addItem(
                                              item: CartItemModel(
                                                productId: productId,
                                                name: product?.name,
                                                price: product?.originalPrice,
                                                imageUrl: product?.imageUrl,
                                                quantity: 1,
                                                facilityId: product?.facilityId,
                                                facilityName:
                                                    product?.facilityName,
                                                unit: 'item',
                                                discountedPrice: product?.price,
                                                skuID: product?.skuID,
                                                availableQuantity:
                                                    product?.availableQty,
                                                maxQuantity: product?.maxLimit,
                                              ),
                                              screen: screen));
                                    },
                                    child: Stack(
                                      alignment: Alignment.center,
                                      children: [
                                        Container(
                                          width: double.infinity,
                                          height: MediaQuery.of(context)
                                                  .size
                                                  .width *
                                              0.09,
                                          decoration: BoxDecoration(
                                            borderRadius:
                                                BorderRadius.circular(4),
                                            gradient: LinearGradient(
                                              colors: [
                                                Color(0xFF2E3964),
                                                Color(0xFFBF4B60),
                                              ],
                                              begin: Alignment.topCenter,
                                              end: Alignment.bottomCenter,
                                            ),
                                          ),
                                        ),
                                        Container(
                                          width: double.infinity,
                                          height: (MediaQuery.of(context)
                                                      .size
                                                      .width *
                                                  0.09) -
                                              2.5,
                                          margin: EdgeInsets.all(1.5),
                                          padding: const EdgeInsets.symmetric(
                                              horizontal: 8, vertical: 4),
                                          decoration: BoxDecoration(
                                            color: AppColors.white,
                                            border: Border.all(
                                                color: Colors.transparent),
                                            borderRadius:
                                                BorderRadius.circular(4),
                                          ),
                                          alignment: Alignment.center,
                                          child: const CustomText(
                                            'Add',
                                            color: AppColors.primary,
                                            fontWeight: FontWeight.w600,
                                            fontSize: 12,
                                          ),
                                        ),
                                      ],
                                    ),
                                  );
                          },
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  margin: EdgeInsets.symmetric(horizontal: 8),
                  padding: EdgeInsets.symmetric(
                      horizontal: ((product?.originalPrice ?? 0) >
                              (product?.price ?? 0))
                          ? 16
                          : 24,
                      vertical: 4),
                  decoration: BoxDecoration(
                    gradient: RadialGradient(
                      colors: [Color(0xFF82CD47), Color(0xFF379237)],
                      radius: 0.8,
                      center: Alignment.topCenter,
                    ),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: FittedBox(
                    child: RichText(
                        text: TextSpan(
                            text:
                                '₹${(product?.price ?? 0).toStringAsFixed(0)} ',
                            children: [
                              // Only show original price if it's greater than current price
                              if ((product?.originalPrice ?? 0) >
                                  (product?.price ?? 0))
                                TextSpan(
                                    text:
                                        ' ₹${(product?.originalPrice ?? 0).toStringAsFixed(0)}',
                                    style: TextStyle(
                                      fontSize: 12,
                                      fontWeight: FontWeight.w400,
                                      color: Colors.white,
                                      decoration: TextDecoration.lineThrough,
                                    )),
                            ],
                            style: TextStyle(
                              color: Color(0XFFFFE31A),
                              fontSize: 16,
                              fontWeight: FontWeight.w700,
                            ))),
                  ),
                ),
              ],
            ),
          );
  }
}
