import 'package:flutter/material.dart';
import 'package:rozana/domain/entities/product_entity.dart';

import '../../../../core/utils/app_dimensions.dart';
import '../../../products/presentation/widgets/product_card.dart';

class ProductGrid extends StatelessWidget {
  const ProductGrid({
    super.key,
    required this.productList,
    this.useEnhancedShimmer = true,
  });

  final List<ProductEntity>? productList;
  final bool useEnhancedShimmer;

  @override
  Widget build(BuildContext context) {
    // Show shimmer loading state when productList is null
    if (productList == null) {
      return RepaintBoundary(
        child: Semantics(
          label: 'Loading products',
          hint: 'Please wait while products are loading',
          child: GridView.builder(
            shrinkWrap: true,
            primary: false,
            padding:
                EdgeInsets.symmetric(horizontal: AppDimensions.screenHzPadding),
            itemCount: 6, // Show 6 shimmer items when loading
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 0.60,
              mainAxisSpacing: 20,
              crossAxisSpacing: 10,
            ),
            itemBuilder: (ctx, index) {
              return DiscountedProductCard(
                product: null,
                isLoading: true,
                staggeredLoading: useEnhancedShimmer,
                staggerIndex: index,
              );
            },
          ),
        ),
      );
    }

    // Show actual products when data is loaded
    return RepaintBoundary(
      child: Semantics(
        label: 'Product grid',
        hint: '${productList!.length} products available',
        child: GridView.builder(
          shrinkWrap: true,
          primary: false,
          padding:
              EdgeInsets.symmetric(horizontal: AppDimensions.screenHzPadding),
          itemCount: productList!.length,
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            childAspectRatio: 0.60,
            mainAxisSpacing: 20,
            crossAxisSpacing: 10,
          ),
          itemBuilder: (ctx, index) {
            return DiscountedProductCard(
              product: productList![index],
              isLoading: false,
            );
          },
        ),
      ),
    );
  }
}
