import 'dart:async';
import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';

import '../dependency_injection/di_container.dart';
import '../services/app_preferences_service.dart';
import '../services/token_refresh_service.dart';
import '../utils/logger.dart';

// ======================= Token Interceptor =======================
class TokenInterceptor extends Interceptor {
  late final TokenRefreshService _tokenRefreshService;
  bool _isRefreshing = false;
  Completer<String>? _refreshCompleter;

  // Queue of requests that are waiting for token refresh
  final List<_RetryRequest> _queue = [];

  TokenInterceptor() {
    _tokenRefreshService = getIt<TokenRefreshService>();
  }

  @override
  void onRequest(
      RequestOptions options, RequestInterceptorHandler handler) async {
    final token = AppPreferences.getToken();
    if ((token ?? '').isNotEmpty) {
      options.headers['Authorization'] = token;
    }
    super.onRequest(options, handler);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    // Check if the error is due to an unauthorized request (401)
    if (err.response?.statusCode == 401) {
      final options = err.requestOptions;

      final retryCount = options.extra['retry_count'] as int? ?? 0;
      if (retryCount >= 1) {
        super.onError(err, handler);
        return;
      }

      // If already refreshing, queue this request
      if (_isRefreshing) {
        final completer = Completer<Response<dynamic>>();
        _queue.add(_RetryRequest(options, completer));

        try {
          final response = await completer.future;
          handler.resolve(response);
          return;
        } catch (e) {
          super.onError(err, handler);
          return;
        }
      }

      // Try to refresh the token and retry the request
      try {
        final newToken = await _refreshToken();

        // Update the request header with the new token
        options.headers['Authorization'] = newToken;

        // Mark this as a retry attempt
        options.extra['retry_count'] = retryCount + 1;

        // Retry the request with the new token using the same Dio instance
        final response = await _retryWithSameDio(options);
        handler.resolve(response);
        return;
      } catch (e) {
        // If token refresh fails, pass the original error
        LogMessage.p('Token refresh failed: $e');
      }
    }

    // For other errors or if token refresh fails, proceed with the original error
    super.onError(err, handler);
  }

  /// Refreshes the token and processes any queued requests
  Future<String> _refreshToken() async {
    // If already refreshing, wait for it to complete
    if (_isRefreshing && _refreshCompleter != null) {
      return await _refreshCompleter!.future;
    }

    try {
      _isRefreshing = true;
      _refreshCompleter = Completer<String>();

      // Refresh the token
      final newToken = await _tokenRefreshService.refreshToken();

      // Process any queued requests with the new token
      _processQueue(newToken);

      // Complete the refresh completer
      _refreshCompleter!.complete(newToken);

      return newToken;
    } catch (e) {
      LogMessage.p('Token refresh failed: $e');

      // Complete the refresh completer with error
      if (_refreshCompleter != null && !_refreshCompleter!.isCompleted) {
        _refreshCompleter!.completeError(e);
      }

      rethrow;
    } finally {
      _isRefreshing = false;
      _refreshCompleter = null;
    }
  }

  /// Retry a request with updated options using the same Dio instance
  /// This ensures that all interceptors are applied to the retry request
  Future<Response<dynamic>> _retryWithSameDio(RequestOptions options) async {
    // Create a new Dio instance with the same configuration but without TokenInterceptor
    // to avoid infinite recursion
    final retryDio = Dio();

    // Copy base configuration
    retryDio.options.baseUrl = options.baseUrl;
    retryDio.options.connectTimeout = options.connectTimeout;
    retryDio.options.receiveTimeout = options.receiveTimeout;
    retryDio.options.sendTimeout = options.sendTimeout;

    // Add only logging interceptor, not token interceptor to avoid recursion
    retryDio.interceptors.add(LoggingInterceptor(tag: 'RETRY'));

    final response = await retryDio.request<dynamic>(
      options.path,
      data: options.data,
      queryParameters: options.queryParameters,
      options: Options(
        method: options.method,
        headers: options.headers,
        responseType: options.responseType,
        contentType: options.contentType,
        validateStatus: options.validateStatus,
        receiveTimeout: options.receiveTimeout,
        sendTimeout: options.sendTimeout,
        extra: options.extra,
      ),
    );

    return response;
  }

  /// Process queued requests with a new token
  void _processQueue(String token) {
    for (var request in _queue) {
      request.options.headers['Authorization'] = token;
      // Use async processing for queued requests
      _retryWithSameDio(request.options).then((response) {
        request.completer.complete(response);
      }).catchError((error) {
        request.completer.completeError(error);
      });
    }

    _queue.clear();
  }
}

/// Helper class to store retry request information
class _RetryRequest {
  final RequestOptions options;
  final Completer<Response<dynamic>> completer;

  _RetryRequest(this.options, this.completer);
}

// ======================= Logging Interceptor =======================
class LoggingInterceptor extends Interceptor {
  final String tag;

  LoggingInterceptor({required this.tag});

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    LogMessage.p(
        "REQUEST [${options.method}] => ${options.baseUrl}${options.path}",
        subTag: tag,
        color: Colors.white);
    LogMessage.p("HEADERS: ${jsonEncode(options.headers)}", subTag: tag);
    LogMessage.p("QUERY: ${jsonEncode(options.queryParameters)}",
        subTag: tag, color: Colors.orange);
    LogMessage.p("BODY: ${jsonEncode(options.data)}",
        subTag: tag, color: Colors.orange);
    super.onRequest(options, handler);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    LogMessage.l(
        "RESPONSE [${response.statusCode}] => ${jsonEncode(response.data)}",
        subTag: tag,
        color: Colors.green);
    super.onResponse(response, handler);
  }
}
