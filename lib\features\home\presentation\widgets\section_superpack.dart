import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:rozana/core/utils/app_dimensions.dart';
import 'package:rozana/domain/entities/category_entity.dart';
import 'package:rozana/widgets/custom_image.dart';
import 'package:rozana/widgets/custom_text.dart';

import '../../../../core/services/appflyer_services/appflyer_events.dart';
import '../../../../routes/app_router.dart';
import '../../bloc/home bloc/home_bloc.dart';

class SuperPackSection extends StatelessWidget {
  const SuperPackSection({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HomeBloc, HomeState>(
      buildWhen: (previous, current) {
        if (previous is! HomeLoaded) {
          return true;
        }
        if (current is HomeLoaded) {
          return previous.categories != current.categories;
        }
        return false; // Don’t rebuild for other transitions
      },
      builder: (context, state) {
        List<CategoryEntity>? categories =
            state.mapOrNull(loaded: (value) => value.categories);
        return Visibility(
          visible: categories?.isNotEmpty ?? false,
          child: Padding(
            padding: EdgeInsets.fromLTRB(AppDimensions.screenHzPadding, 40,
                AppDimensions.screenHzPadding, 30),
            child: Stack(
              alignment: Alignment.topRight,
              children: [
                Container(
                  width: double.infinity,
                  margin: EdgeInsets.only(top: 19),
                  decoration: BoxDecoration(
                    color: Color(0xFF1D9B24),
                    image: DecorationImage(
                        fit: BoxFit.fill,
                        image: AssetImage(
                            'assets/images/background_texture_3.png')),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Column(
                    children: [
                      Padding(
                        padding: const EdgeInsets.fromLTRB(15, 22, 15, 20),
                        child: Image.asset(
                          'assets/images/superpack_text.png',
                          width: MediaQuery.of(context).size.width * 0.52,
                        ),
                      ),
                      Wrap(
                          spacing: 10,
                          runSpacing: 10,
                          children: List.generate(
                              ((categories?.length ?? 0) > 6)
                                  ? 6
                                  : (categories?.length ?? 0), (i) {
                            CategoryEntity? category = categories?[i];
                            return GestureDetector(
                              onTap: () async {
                                HapticFeedback.lightImpact();
                                context.push(RouteNames.products, extra: {
                                  'categoryId': category?.id,
                                  'categoryName': category?.name,
                                });
                                // Log category view event to AppsFlyer
                                await AppsFlyerEvents.categoryView(
                                  category?.id ?? '',
                                  category?.name ?? '',
                                );
                              },
                              child: Container(
                                width:
                                    (MediaQuery.of(context).size.width - 90) /
                                        3,
                                height:
                                    (MediaQuery.of(context).size.width - 90) /
                                        3,
                                padding: EdgeInsets.all(10),
                                decoration: BoxDecoration(
                                  color: Color(0xFFFEDA00),
                                  borderRadius: BorderRadius.circular(5),
                                ),
                                child: Column(
                                  children: [
                                    Expanded(
                                        child: CustomImage(
                                            imageUrl:
                                                category?.imageUrl ?? '')),
                                    SizedBox(height: 4),
                                    ConstrainedBox(
                                      constraints:
                                          BoxConstraints(minHeight: 20),
                                      child: Center(
                                        child: CustomText(
                                          category?.name ?? '',
                                          fontSize: 10,
                                          fontWeight: FontWeight.w800,
                                          maxLines: 2,
                                          textHeight: 1,
                                          textAlign: TextAlign.center,
                                        ),
                                      ),
                                    )
                                  ],
                                ),
                              ),
                            );
                          })),
                      SizedBox(height: 20),
                    ],
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(right: 10),
                  child: Image.asset(
                    'assets/images/cashback_label.png',
                    width: 120,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
