class NewProductModel {
  final String brandId;
  final String brandName;
  final String categoryId;
  final String categoryName;
  final String description;
  final String name;
  final List<String> photos;
  final int totalCount;
  final List<VariantModel> variants;

  NewProductModel({
    required this.brandId,
    required this.brandName,
    required this.categoryId,
    required this.categoryName,
    required this.description,
    required this.name,
    required this.photos,
    required this.totalCount,
    required this.variants,
  });

  factory NewProductModel.fromMap(Map<String, dynamic> map) {
    return NewProductModel(
      brandId: map['brand_id'] ?? '',
      brandName: map['brand_name'] ?? '',
      categoryId: map['category_id'] ?? '',
      categoryName: map['category_name'] ?? '',
      description: map['description'] ?? '',
      name: map['name'] ?? '',
      photos: map['photos'] != null ? List<String>.from(map['photos']) : [],
      totalCount: map['total_count'] ?? 0,
      variants: map['variants'] != null
          ? List<VariantModel>.from(
              map['variants'].map((v) => VariantModel.fromMap(v)))
          : [],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'brand_id': brandId,
      'brand_name': brandName,
      'category_id': categoryId,
      'category_name': categoryName,
      'description': description,
      'name': name,
      'photos': photos,
      'total_count': totalCount,
      'variants': variants.map((v) => v.toMap()).toList(),
    };
  }
}

class VariantModel {
  final String variantName;
  final dynamic availableQty;
  final dynamic maxPurchaseLimit;
  final dynamic mrp;
  final String id;
  final dynamic sellingPrice;

  VariantModel({
    required this.variantName,
    required this.availableQty,
    required this.maxPurchaseLimit,
    required this.mrp,
    required this.id,
    required this.sellingPrice,
  });

  factory VariantModel.fromMap(Map<String, dynamic> map) {
    return VariantModel(
      variantName: map['variant_name'] ?? '',
      availableQty: map['available_qty'],
      maxPurchaseLimit: map['max_purchase_limit'],
      mrp: map['mrp'],
      id: map['id'],
      sellingPrice: map['selling_price'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'variant_name': variantName,
      'available_qty': availableQty,
      'max_purchase_limit': maxPurchaseLimit,
      'mrp': mrp,
      'id': id,
      'selling_price': sellingPrice,
    };
  }
}
