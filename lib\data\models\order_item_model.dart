class OrderItemModel {
  String? id;
  String? productId;
  String? name;
  String? imageUrl;
  num? price;
  int? quantity;
  String? unit;
  num? discountedPrice;
  String? facilityId;
  String? facilityName;
  String? skuID;
  String? status;

  OrderItemModel({
    this.id,
    this.productId,
    this.name,
    this.imageUrl,
    this.price,
    this.quantity,
    this.unit,
    this.discountedPrice,
    this.facilityId,
    this.facilityName,
    this.skuID,
    this.status,
  });

  OrderItemModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    productId = json['productId'];
    name =
        json['name'] ?? 'Unknown Product'; // Provide default for missing name
    imageUrl = json['imageUrl']; // Can be null

    // Map API fields: unit_price -> price
    final priceValue = json['unit_price'] ?? json['price'];
    if (priceValue is num) {
      price = priceValue;
    } else if (priceValue != null) {
      var numb = num.tryParse(priceValue.toString());
      if (numb is num) {
        price = numb;
      }
    }

    if (json['quantity'] is int) {
      quantity = json['quantity'];
    } else if (json['quantity'] != null) {
      var numb = int.tryParse(json['quantity']!.toString());
      if (numb is int) {
        quantity = numb;
      }
    }

    unit = json['unit'];

    // Map API fields: sale_price -> discountedPrice
    final discountedPriceValue = json['sale_price'] ?? json['discountedPrice'];
    if (discountedPriceValue is num) {
      discountedPrice = discountedPriceValue;
    } else if (discountedPriceValue != null) {
      var numb = num.tryParse(discountedPriceValue.toString());
      if (numb is num) {
        discountedPrice = numb;
      }
    }

    facilityId = json['facilityId'];
    facilityName = json['facilityName'];
    // Map API fields: sku -> skuID
    skuID = json['sku'] ?? json['skuID'];
    status = json['status']?.toString();

    // If name is still null/empty and we have SKU, use SKU as name
    if ((name == null || name!.isEmpty || name == 'Unknown Product') &&
        skuID != null) {
      name = 'Product $skuID';
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['productId'] = productId;
    data['name'] = name;
    data['imageUrl'] = imageUrl;
    data['price'] = price;
    data['quantity'] = quantity;
    data['unit'] = unit;
    data['discountedPrice'] = discountedPrice;
    data['facilityId'] = facilityId;
    data['facilityName'] = facilityName;
    data['skuID'] = skuID;
    data['status'] = status;
    return data;
  }

  OrderItemModel copyWith({
    String? id,
    String? productId,
    String? name,
    String? imageUrl,
    num? price,
    int? quantity,
    String? unit,
    num? discountedPrice,
    String? facilityId,
    String? facilityName,
    String? skuID,
    String? status,
  }) {
    return OrderItemModel(
      id: id ?? this.id,
      productId: productId ?? this.productId,
      name: name ?? this.name,
      imageUrl: imageUrl ?? this.imageUrl,
      price: price ?? this.price,
      quantity: quantity ?? this.quantity,
      unit: unit ?? this.unit,
      discountedPrice: discountedPrice ?? this.discountedPrice,
      facilityId: facilityId ?? this.facilityId,
      facilityName: facilityName ?? this.facilityName,
      skuID: skuID ?? this.skuID,
      status: status ?? this.status,
    );
  }

  /// Calculate total price for this item
  num get totalPrice => (discountedPrice ?? price ?? 0) * (quantity ?? 0);
}
