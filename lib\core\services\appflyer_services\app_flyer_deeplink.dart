import 'dart:convert';

import 'package:appsflyer_sdk/appsflyer_sdk.dart';
import 'package:rozana/core/services/appflyer_services/app_flyers_services.dart';
import 'package:rozana/core/utils/logger.dart';
import 'package:share_plus/share_plus.dart';

import '../../../routes/app_router.dart';

class AppFlyerDeeplink {
  static final String baseLink = "rozanatest://open";
  static final String domainUrl = "rozanatest.onelink.me";
  static void onDeepLink(
    Function(String, Map<String, dynamic>) onDeepLinkFound,
  ) {
    AppsFlyerServices.appsflyerSdk?.onDeepLinking((DeepLinkResult result) {
      if (result.status == Status.FOUND) {
        handleDeepLink(result, onDeepLinkFound: onDeepLinkFound);
      }
    });
  }

  static void handleDeepLink(DeepLinkResult result,
      {Function(String, Map<String, dynamic>)? onDeepLinkFound}) {
    Map<String, dynamic>? deepLink = result.deepLink?.clickEvent;

    // WidgetsBinding.instance.addPostFrameCallback((_) {
    switch (deepLink?['screen'] ?? '-') {
      case RouteNames.productDetail:
        Map<String, dynamic> arguments = {
          'product': jsonDecode(deepLink?['product'] ?? {}),
        };
        AppsFlyerServices.pendingLink = RouteNames.productDetail;
        AppsFlyerServices.pendingLinkArguments = arguments;
        onDeepLinkFound?.call(RouteNames.productDetail, arguments);
        break;
    }
  }

  static void createDeepLink({
    Map<String, String>? data,
    String? name,
    String? imageUrl,
  }) {
    AppsFlyerServices.appsflyerSdk?.generateInviteLink(
      AppsFlyerInviteLinkParams(
        baseDeepLink: AppFlyerDeeplink.baseLink,
        brandDomain: AppFlyerDeeplink.domainUrl,
        channel: 'Flutter_app',
        campaign: 'Flutter_app_share',
        referrerImageUrl: imageUrl,
        customParams: data,
      ),
      (value) async {
        await Share.share(
          value['payload']['userInviteURL'],
          subject: 'Checkout this product: $name',
        );
      },
      (e) {
        LogMessage.p('Error creating deep link: $e');
      },
    );
  }
}
