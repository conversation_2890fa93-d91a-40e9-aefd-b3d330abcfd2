import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:rozana/core/themes/color_schemes.dart';
import 'package:rozana/core/widgets/image_page_view.dart';
import 'package:rozana/routes/app_router.dart';

class ProductImageSlider extends StatefulWidget {
  final List<String> images;
  final double aspectRatio;
  final bool showIndicators;

  const ProductImageSlider({
    super.key,
    required this.images,
    this.aspectRatio = 1.0,
    this.showIndicators = true,
  });

  @override
  State<ProductImageSlider> createState() => _ProductImageSliderState();
}

class _ProductImageSliderState extends State<ProductImageSlider> {
  late PageController _pageController;
  int _currentIndex = 0;

  static const Duration _animationDuration = Duration(milliseconds: 300);
  static const Curve _animationCurve = Curves.easeInOut;
  static const double _borderRadius = 8.0;
  static const double _navigationButtonSize = 32.0;
  static const double _navigationButtonIconSize = 20.0;
  static const double _indicatorSize = 8.0;
  static const double _indicatorSpacing = 3.0;
  static const String _placeholderAsset = 'assets/products/vegetables.png';

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // If no images or empty list, show placeholder
    if (widget.images.isEmpty) {
      return _buildPlaceholderImage();
    }

    // If only one image, show it without PageView
    if (widget.images.length == 1) {
      return _buildSingleImage(widget.images.first);
    }

    // Multiple images - show slider
    return _buildImageSlider();
  }

  /// Common method to build ImagePageView with consistent styling
  Widget _buildImagePageView({
    required List<String> images,
    required Function(int) onImageTap,
    PageController? controller,
    Function(int)? onPageChanged,
  }) {
    return AspectRatio(
      aspectRatio: widget.aspectRatio,
      child: ImagePageView(
        images: images,
        controller: controller,
        onPageChanged: onPageChanged,
        onImageTap: onImageTap,
        fit: BoxFit.cover,
        borderRadius: BorderRadius.circular(_borderRadius),
        backgroundColor: Colors.grey[200],
      ),
    );
  }

  Widget _buildPlaceholderImage() {
    return _buildImagePageView(
      images: const [], // Empty list will show placeholder
      onImageTap: (index) => _openFullScreenViewer(0),
    );
  }

  Widget _buildSingleImage(String imageUrl) {
    return _buildImagePageView(
      images: [imageUrl],
      onImageTap: (index) => _openFullScreenViewer(0),
    );
  }

  Widget _buildImageSlider() {
    return Stack(
      children: [
        _buildImagePageView(
          images: widget.images,
          controller: _pageController,
          onPageChanged: (index) {
            setState(() {
              _currentIndex = index;
            });
          },
          onImageTap: (index) => _openFullScreenViewer(index),
        ),
          
          // Page indicators
          if (widget.showIndicators && widget.images.length > 1)
            Positioned(
              bottom: 2,
              left: 0,
              right: 0,
              child: _buildPageIndicators(),
            ),
          
          
          // Navigation arrows (optional - only show if more than 1 image)
          if (widget.images.length > 1) ...[
            _buildPositionedNavigationButton(
              isLeft: true,
              icon: Icons.chevron_left,
              onTap: _previousImage,
            ),
            _buildPositionedNavigationButton(
              isLeft: false,
              icon: Icons.chevron_right,
              onTap: _nextImage,
            ),
          ],
      ],
    );
  }

  Widget _buildPageIndicators() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(
        widget.images.length,
        (index) => Container(
          margin: const EdgeInsets.symmetric(horizontal: _indicatorSpacing),
          width: _indicatorSize,
          height: _indicatorSize,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: _currentIndex == index
                ? AppColors.primary
                : AppColors.primary.withAlpha(80),
          ),
        ),
      ),
    );
  }


  /// Common method for building positioned navigation buttons
  Widget _buildPositionedNavigationButton({
    required bool isLeft,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return Positioned(
      left: isLeft ? 8 : null,
      right: isLeft ? null : 8,
      top: 0,
      bottom: 0,
      child: Center(
        child: _buildNavigationButton(
          icon: icon,
          onTap: onTap,
        ),
      ),
    );
  }

  Widget _buildNavigationButton({
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: _navigationButtonSize,
        height: _navigationButtonSize,
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.5),
          shape: BoxShape.circle,
        ),
        child: Icon(
          icon,
          color: Colors.white,
          size: _navigationButtonIconSize,
        ),
      ),
    );
  }

  /// Common method for page navigation with consistent animation
  void _navigateToPage(int targetIndex) {
    if (targetIndex >= 0 && targetIndex < widget.images.length) {
      _pageController.animateToPage(
        targetIndex,
        duration: _animationDuration,
        curve: _animationCurve,
      );
    }
  }

  void _previousImage() {
    _navigateToPage(_currentIndex - 1);
  }

  void _nextImage() {
    _navigateToPage(_currentIndex + 1);
  }

  void _openFullScreenViewer(int initialIndex) {
    // If no images, use placeholder
    final imagesToShow = widget.images.isEmpty
        ? [_placeholderAsset]
        : widget.images;

    context.push(RouteNames.fullScreenImageViewer, extra: {
      'images': imagesToShow,
      'initialIndex': initialIndex,
    });
  }
}
