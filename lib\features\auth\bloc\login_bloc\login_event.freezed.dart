// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'login_event.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$LoginEvent {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is LoginEvent);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'LoginEvent()';
  }
}

/// @nodoc
class $LoginEventCopyWith<$Res> {
  $LoginEventCopyWith(LoginEvent _, $Res Function(LoginEvent) __);
}

/// Adds pattern-matching-related methods to [LoginEvent].
extension LoginEventPatterns on LoginEvent {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_InitLogin value)? initLogin,
    TResult Function(_MobileChanged value)? mobileChanged,
    TResult Function(_SubmitLogin value)? submitLogin,
    TResult Function(_FailedLogin value)? loginFailed,
    TResult Function(_InitOTP value)? initOTP,
    TResult Function(_OTPTimer value)? updateTimer,
    TResult Function(_OTPResend value)? resendOTP,
    TResult Function(_OtpChanged value)? otpChanged,
    TResult Function(_SubmitOTP value)? submitOTP,
    TResult Function(_FailedOTP value)? otpFailed,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _InitLogin() when initLogin != null:
        return initLogin(_that);
      case _MobileChanged() when mobileChanged != null:
        return mobileChanged(_that);
      case _SubmitLogin() when submitLogin != null:
        return submitLogin(_that);
      case _FailedLogin() when loginFailed != null:
        return loginFailed(_that);
      case _InitOTP() when initOTP != null:
        return initOTP(_that);
      case _OTPTimer() when updateTimer != null:
        return updateTimer(_that);
      case _OTPResend() when resendOTP != null:
        return resendOTP(_that);
      case _OtpChanged() when otpChanged != null:
        return otpChanged(_that);
      case _SubmitOTP() when submitOTP != null:
        return submitOTP(_that);
      case _FailedOTP() when otpFailed != null:
        return otpFailed(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_InitLogin value) initLogin,
    required TResult Function(_MobileChanged value) mobileChanged,
    required TResult Function(_SubmitLogin value) submitLogin,
    required TResult Function(_FailedLogin value) loginFailed,
    required TResult Function(_InitOTP value) initOTP,
    required TResult Function(_OTPTimer value) updateTimer,
    required TResult Function(_OTPResend value) resendOTP,
    required TResult Function(_OtpChanged value) otpChanged,
    required TResult Function(_SubmitOTP value) submitOTP,
    required TResult Function(_FailedOTP value) otpFailed,
  }) {
    final _that = this;
    switch (_that) {
      case _InitLogin():
        return initLogin(_that);
      case _MobileChanged():
        return mobileChanged(_that);
      case _SubmitLogin():
        return submitLogin(_that);
      case _FailedLogin():
        return loginFailed(_that);
      case _InitOTP():
        return initOTP(_that);
      case _OTPTimer():
        return updateTimer(_that);
      case _OTPResend():
        return resendOTP(_that);
      case _OtpChanged():
        return otpChanged(_that);
      case _SubmitOTP():
        return submitOTP(_that);
      case _FailedOTP():
        return otpFailed(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_InitLogin value)? initLogin,
    TResult? Function(_MobileChanged value)? mobileChanged,
    TResult? Function(_SubmitLogin value)? submitLogin,
    TResult? Function(_FailedLogin value)? loginFailed,
    TResult? Function(_InitOTP value)? initOTP,
    TResult? Function(_OTPTimer value)? updateTimer,
    TResult? Function(_OTPResend value)? resendOTP,
    TResult? Function(_OtpChanged value)? otpChanged,
    TResult? Function(_SubmitOTP value)? submitOTP,
    TResult? Function(_FailedOTP value)? otpFailed,
  }) {
    final _that = this;
    switch (_that) {
      case _InitLogin() when initLogin != null:
        return initLogin(_that);
      case _MobileChanged() when mobileChanged != null:
        return mobileChanged(_that);
      case _SubmitLogin() when submitLogin != null:
        return submitLogin(_that);
      case _FailedLogin() when loginFailed != null:
        return loginFailed(_that);
      case _InitOTP() when initOTP != null:
        return initOTP(_that);
      case _OTPTimer() when updateTimer != null:
        return updateTimer(_that);
      case _OTPResend() when resendOTP != null:
        return resendOTP(_that);
      case _OtpChanged() when otpChanged != null:
        return otpChanged(_that);
      case _SubmitOTP() when submitOTP != null:
        return submitOTP(_that);
      case _FailedOTP() when otpFailed != null:
        return otpFailed(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initLogin,
    TResult Function(String value)? mobileChanged,
    TResult Function()? submitLogin,
    TResult Function()? loginFailed,
    TResult Function(String verificationId, String mobileNumber)? initOTP,
    TResult Function(int second)? updateTimer,
    TResult Function()? resendOTP,
    TResult Function(String value)? otpChanged,
    TResult Function()? submitOTP,
    TResult Function()? otpFailed,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _InitLogin() when initLogin != null:
        return initLogin();
      case _MobileChanged() when mobileChanged != null:
        return mobileChanged(_that.value);
      case _SubmitLogin() when submitLogin != null:
        return submitLogin();
      case _FailedLogin() when loginFailed != null:
        return loginFailed();
      case _InitOTP() when initOTP != null:
        return initOTP(_that.verificationId, _that.mobileNumber);
      case _OTPTimer() when updateTimer != null:
        return updateTimer(_that.second);
      case _OTPResend() when resendOTP != null:
        return resendOTP();
      case _OtpChanged() when otpChanged != null:
        return otpChanged(_that.value);
      case _SubmitOTP() when submitOTP != null:
        return submitOTP();
      case _FailedOTP() when otpFailed != null:
        return otpFailed();
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initLogin,
    required TResult Function(String value) mobileChanged,
    required TResult Function() submitLogin,
    required TResult Function() loginFailed,
    required TResult Function(String verificationId, String mobileNumber)
        initOTP,
    required TResult Function(int second) updateTimer,
    required TResult Function() resendOTP,
    required TResult Function(String value) otpChanged,
    required TResult Function() submitOTP,
    required TResult Function() otpFailed,
  }) {
    final _that = this;
    switch (_that) {
      case _InitLogin():
        return initLogin();
      case _MobileChanged():
        return mobileChanged(_that.value);
      case _SubmitLogin():
        return submitLogin();
      case _FailedLogin():
        return loginFailed();
      case _InitOTP():
        return initOTP(_that.verificationId, _that.mobileNumber);
      case _OTPTimer():
        return updateTimer(_that.second);
      case _OTPResend():
        return resendOTP();
      case _OtpChanged():
        return otpChanged(_that.value);
      case _SubmitOTP():
        return submitOTP();
      case _FailedOTP():
        return otpFailed();
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initLogin,
    TResult? Function(String value)? mobileChanged,
    TResult? Function()? submitLogin,
    TResult? Function()? loginFailed,
    TResult? Function(String verificationId, String mobileNumber)? initOTP,
    TResult? Function(int second)? updateTimer,
    TResult? Function()? resendOTP,
    TResult? Function(String value)? otpChanged,
    TResult? Function()? submitOTP,
    TResult? Function()? otpFailed,
  }) {
    final _that = this;
    switch (_that) {
      case _InitLogin() when initLogin != null:
        return initLogin();
      case _MobileChanged() when mobileChanged != null:
        return mobileChanged(_that.value);
      case _SubmitLogin() when submitLogin != null:
        return submitLogin();
      case _FailedLogin() when loginFailed != null:
        return loginFailed();
      case _InitOTP() when initOTP != null:
        return initOTP(_that.verificationId, _that.mobileNumber);
      case _OTPTimer() when updateTimer != null:
        return updateTimer(_that.second);
      case _OTPResend() when resendOTP != null:
        return resendOTP();
      case _OtpChanged() when otpChanged != null:
        return otpChanged(_that.value);
      case _SubmitOTP() when submitOTP != null:
        return submitOTP();
      case _FailedOTP() when otpFailed != null:
        return otpFailed();
      case _:
        return null;
    }
  }
}

/// @nodoc

class _InitLogin implements LoginEvent {
  const _InitLogin();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _InitLogin);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'LoginEvent.initLogin()';
  }
}

/// @nodoc

class _MobileChanged implements LoginEvent {
  const _MobileChanged(this.value);

  final String value;

  /// Create a copy of LoginEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$MobileChangedCopyWith<_MobileChanged> get copyWith =>
      __$MobileChangedCopyWithImpl<_MobileChanged>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _MobileChanged &&
            (identical(other.value, value) || other.value == value));
  }

  @override
  int get hashCode => Object.hash(runtimeType, value);

  @override
  String toString() {
    return 'LoginEvent.mobileChanged(value: $value)';
  }
}

/// @nodoc
abstract mixin class _$MobileChangedCopyWith<$Res>
    implements $LoginEventCopyWith<$Res> {
  factory _$MobileChangedCopyWith(
          _MobileChanged value, $Res Function(_MobileChanged) _then) =
      __$MobileChangedCopyWithImpl;
  @useResult
  $Res call({String value});
}

/// @nodoc
class __$MobileChangedCopyWithImpl<$Res>
    implements _$MobileChangedCopyWith<$Res> {
  __$MobileChangedCopyWithImpl(this._self, this._then);

  final _MobileChanged _self;
  final $Res Function(_MobileChanged) _then;

  /// Create a copy of LoginEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? value = null,
  }) {
    return _then(_MobileChanged(
      null == value
          ? _self.value
          : value // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _SubmitLogin implements LoginEvent {
  const _SubmitLogin();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _SubmitLogin);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'LoginEvent.submitLogin()';
  }
}

/// @nodoc

class _FailedLogin implements LoginEvent {
  const _FailedLogin();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _FailedLogin);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'LoginEvent.loginFailed()';
  }
}

/// @nodoc

class _InitOTP implements LoginEvent {
  const _InitOTP(this.verificationId, this.mobileNumber);

  final String verificationId;
  final String mobileNumber;

  /// Create a copy of LoginEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$InitOTPCopyWith<_InitOTP> get copyWith =>
      __$InitOTPCopyWithImpl<_InitOTP>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _InitOTP &&
            (identical(other.verificationId, verificationId) ||
                other.verificationId == verificationId) &&
            (identical(other.mobileNumber, mobileNumber) ||
                other.mobileNumber == mobileNumber));
  }

  @override
  int get hashCode => Object.hash(runtimeType, verificationId, mobileNumber);

  @override
  String toString() {
    return 'LoginEvent.initOTP(verificationId: $verificationId, mobileNumber: $mobileNumber)';
  }
}

/// @nodoc
abstract mixin class _$InitOTPCopyWith<$Res>
    implements $LoginEventCopyWith<$Res> {
  factory _$InitOTPCopyWith(_InitOTP value, $Res Function(_InitOTP) _then) =
      __$InitOTPCopyWithImpl;
  @useResult
  $Res call({String verificationId, String mobileNumber});
}

/// @nodoc
class __$InitOTPCopyWithImpl<$Res> implements _$InitOTPCopyWith<$Res> {
  __$InitOTPCopyWithImpl(this._self, this._then);

  final _InitOTP _self;
  final $Res Function(_InitOTP) _then;

  /// Create a copy of LoginEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? verificationId = null,
    Object? mobileNumber = null,
  }) {
    return _then(_InitOTP(
      null == verificationId
          ? _self.verificationId
          : verificationId // ignore: cast_nullable_to_non_nullable
              as String,
      null == mobileNumber
          ? _self.mobileNumber
          : mobileNumber // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _OTPTimer implements LoginEvent {
  const _OTPTimer(this.second);

  final int second;

  /// Create a copy of LoginEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$OTPTimerCopyWith<_OTPTimer> get copyWith =>
      __$OTPTimerCopyWithImpl<_OTPTimer>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _OTPTimer &&
            (identical(other.second, second) || other.second == second));
  }

  @override
  int get hashCode => Object.hash(runtimeType, second);

  @override
  String toString() {
    return 'LoginEvent.updateTimer(second: $second)';
  }
}

/// @nodoc
abstract mixin class _$OTPTimerCopyWith<$Res>
    implements $LoginEventCopyWith<$Res> {
  factory _$OTPTimerCopyWith(_OTPTimer value, $Res Function(_OTPTimer) _then) =
      __$OTPTimerCopyWithImpl;
  @useResult
  $Res call({int second});
}

/// @nodoc
class __$OTPTimerCopyWithImpl<$Res> implements _$OTPTimerCopyWith<$Res> {
  __$OTPTimerCopyWithImpl(this._self, this._then);

  final _OTPTimer _self;
  final $Res Function(_OTPTimer) _then;

  /// Create a copy of LoginEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? second = null,
  }) {
    return _then(_OTPTimer(
      null == second
          ? _self.second
          : second // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _OTPResend implements LoginEvent {
  const _OTPResend();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _OTPResend);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'LoginEvent.resendOTP()';
  }
}

/// @nodoc

class _OtpChanged implements LoginEvent {
  const _OtpChanged(this.value);

  final String value;

  /// Create a copy of LoginEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$OtpChangedCopyWith<_OtpChanged> get copyWith =>
      __$OtpChangedCopyWithImpl<_OtpChanged>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _OtpChanged &&
            (identical(other.value, value) || other.value == value));
  }

  @override
  int get hashCode => Object.hash(runtimeType, value);

  @override
  String toString() {
    return 'LoginEvent.otpChanged(value: $value)';
  }
}

/// @nodoc
abstract mixin class _$OtpChangedCopyWith<$Res>
    implements $LoginEventCopyWith<$Res> {
  factory _$OtpChangedCopyWith(
          _OtpChanged value, $Res Function(_OtpChanged) _then) =
      __$OtpChangedCopyWithImpl;
  @useResult
  $Res call({String value});
}

/// @nodoc
class __$OtpChangedCopyWithImpl<$Res> implements _$OtpChangedCopyWith<$Res> {
  __$OtpChangedCopyWithImpl(this._self, this._then);

  final _OtpChanged _self;
  final $Res Function(_OtpChanged) _then;

  /// Create a copy of LoginEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? value = null,
  }) {
    return _then(_OtpChanged(
      null == value
          ? _self.value
          : value // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _SubmitOTP implements LoginEvent {
  const _SubmitOTP();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _SubmitOTP);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'LoginEvent.submitOTP()';
  }
}

/// @nodoc

class _FailedOTP implements LoginEvent {
  const _FailedOTP();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _FailedOTP);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'LoginEvent.otpFailed()';
  }
}

// dart format on
