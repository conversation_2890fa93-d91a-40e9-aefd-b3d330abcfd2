import 'package:freezed_annotation/freezed_annotation.dart';
part 'login_event.freezed.dart';

@freezed
class LoginEvent with _$LoginEvent {
  const factory LoginEvent.initLogin() = _InitLogin;
  const factory LoginEvent.mobileChanged(String value) = _MobileChanged;
  const factory LoginEvent.submitLogin() = _SubmitLogin;
  const factory LoginEvent.loginFailed() = _FailedLogin;
  const factory LoginEvent.initOTP(String verificationId, String mobileNumber) =
      _InitOTP;
  const factory LoginEvent.updateTimer(int second) = _OTPTimer;
  const factory LoginEvent.resendOTP() = _OTPResend;
  const factory LoginEvent.otpChanged(String value) = _OtpChanged;
  const factory LoginEvent.submitOTP() = _SubmitOTP;
  const factory LoginEvent.otpFailed() = _FailedOTP;
}
