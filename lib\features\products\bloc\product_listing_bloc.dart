import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rozana/domain/entities/category_entity.dart';

import '../../../domain/usecases/get_products_usecase.dart';
import 'product_listing_event.dart';
import 'product_listing_state.dart';

class ProductListingBloc
    extends Bloc<ProductListingEvent, ProductListingState> {
  final GetProductsUseCase _getProductsUseCase;

  ProductListingBloc({
    required GetProductsUseCase getProductsUseCase,
  })  : _getProductsUseCase = getProductsUseCase,
        super(const ProductListingState.loading()) {
    // Register event handlers using .map for Freezed events
    on<ProductListingEvent>((event, emit) async {
      await event.when(
        initial: (
          CategoryEntity? category,
        ) async {
          await _onInitialEvent(
            category,
            emit,
          );
        },
        selectSubcategory: (CategoryEntity? subcategory) {
          _onSelectSubcategoryEvent(subcategory, emit);
        },
        loadProductsByCategory: (String categoryId, String excludeProductId, int page, int pageSize, bool refresh) async {
          await _onLoadProductsByCategoryEvent(categoryId, excludeProductId, page, pageSize, refresh, emit);
        },
        loadProductsByBrand: (String brandId, String excludeProductId, int page, int pageSize, bool refresh) async {
          await _onLoadProductsByBrandEvent(brandId, excludeProductId, page, pageSize, refresh, emit);
        },

      );
    });
  }

  // Event handler for initial setup
  Future<void> _onInitialEvent(
    CategoryEntity? category,
    Emitter<ProductListingState> emit,
  ) async {
    emit(const ProductListingState.loading());
    emit(ProductListingState.loaded(category: category));
  }

  // Event handler for selecting a subcategory
  void _onSelectSubcategoryEvent(
    CategoryEntity? subCategory,
    Emitter<ProductListingState> emit,
  ) {
    if (subCategory != null) {
      emit(ProductListingState.loaded(
        category: subCategory,
      ));
    }
  }

  // Event handler for loading products by category
  Future<void> _onLoadProductsByCategoryEvent(
    String categoryId,
    String excludeProductId,
    int page,
    int pageSize,
    bool refresh,
    Emitter<ProductListingState> emit,
  ) async {
    try {
      // Emit loading state
      emit(const ProductListingState.productsByCategoryLoading());

      // Call use case to get products using the consolidated execute method
      final products = await _getProductsUseCase.execute(
        categoryId: categoryId,
        page: page,
        pageSize: pageSize,
        refresh: refresh,
        excludeProductId: excludeProductId,
      );

      // Emit loaded state with products
      emit(ProductListingState.productsByCategoryLoaded(
        products: products,
        categoryId: categoryId,
        excludeProductId: excludeProductId,
        hasMore: products.length >= pageSize,
        currentPage: page,
      ));
    } catch (e) {
      // Emit error state
      emit(ProductListingState.productsByCategoryError(e.toString()));
    }
  }

  // Event handler for loading products by brand
  Future<void> _onLoadProductsByBrandEvent(
    String brandId,
    String excludeProductId,
    int page,
    int pageSize,
    bool refresh,
    Emitter<ProductListingState> emit,
  ) async {
    try {
      // Emit loading state
      emit(const ProductListingState.productsByBrandLoading());

      // Call use case to get products using the consolidated execute method
      final products = await _getProductsUseCase.execute(
        brandId: brandId,
        excludeProductId: excludeProductId,
        page: page,
        pageSize: pageSize,
        refresh: refresh,
       
      );

      // Emit loaded state with products
      emit(ProductListingState.productsByBrandLoaded(
        products: products,
        brandId: brandId,
        excludeProductId: excludeProductId,
        hasMore: products.length >= pageSize,
        currentPage: page,
      ));
    } catch (e) {
      // Emit error state
      emit(ProductListingState.productsByBrandError(e.toString()));
    }
  }
}
