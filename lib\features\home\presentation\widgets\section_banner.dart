import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:rozana/domain/entities/banner_entity.dart';
import 'package:rozana/domain/entities/category_entity.dart';
import 'package:rozana/routes/app_router.dart';
import 'package:rozana/widgets/app_carousel.dart';
import 'package:rozana/widgets/skeleton_loader_factory.dart';
import 'package:rozana/widgets/shimmer_widgets.dart';

import '../../../categories/bloc/categories_bloc.dart';

class BannerSection extends StatelessWidget {
  const BannerSection({
    super.key,
    required this.banners,
    this.topPadding = 30,
    this.showIndicator = true,
    this.height = 220,
    this.parentCategory,
  });
  final double topPadding;
  final bool showIndicator;
  final double height;

  final List<BannerEntity>? banners;
  final CategoryEntity? parentCategory;

  @override
  Widget build(BuildContext context) {
    // Return empty widget if banners list is empty and not null
    if (banners != null && banners!.isEmpty) {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: EdgeInsets.only(top: topPadding),
      // AnimatedSwitcher for smooth transition between skeleton and content
      child: AnimatedSwitcher(
        duration: const Duration(milliseconds: 300),
        switchInCurve: Curves.easeInOut,
        switchOutCurve: Curves.easeInOut,
        child: banners == null
            ? _buildSkeletonLoader(context)
            : AppCarousel<BannerEntity?>(
                key: const ValueKey('loaded_banner_carousel'),
                items: banners!,
                height: height,
                viewportFraction: 0.92,
                borderRadius: 12,
                autoPlay: (banners?.length ?? 0) > 1,
                infiniteScroll: (banners?.length ?? 0) > 1,
                autoPlayInterval: const Duration(seconds: 4),
                indicatorType: CarouselIndicatorType.dottedBar,
                showIndicator: showIndicator,
                itemBuilder: (context, banner, index) {
                  return ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: Image.network(
                      banner?.imageUrl ?? '',
                      fit: BoxFit.fill,
                      errorBuilder: (context, error, stackTrace) =>
                          ShimmerBox(),
                    ),
                  );
                },
                onItemTap: (index) async {
                  HapticFeedback.lightImpact();
                  // Using null check to ensure banners is not null
                  // BannerEntity banner = banners![index];

                  CategoriesBloc.loadedSubcategories.clear();

                  // if (banner.hasCategory == true) {
                  //   final category = CategoryEntity(
                  //     id: banner.collectionId!,
                  //     name: banner.name ?? 'Banner Products',
                  //     count: 0,
                  //     level: 'sub_sub_category',
                  //     collectionId: banner.collectionId!,
                  //   );
                  context.push(RouteNames.categories, extra: {
                    'category': parentCategory,
                  });

                  // await AppsFlyerEvents.categoryView(
                  //   category.id,
                  //   category.name,
                  // );
                  // }
                },
              ),
      ),
    );
  }

  // Enhanced skeleton loader for banners with staggered animation effect
  Widget _buildSkeletonLoader(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 30),
      child: Column(
        children: [
          Container(
            height: 220,
            margin: const EdgeInsets.symmetric(horizontal: 16),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12),
              // Using the standardized skeleton loader factory
              child: SkeletonLoaderFactory.createBannerSkeleton(
                height: 220,
                radius: 12,
              ),
            ),
          ),
          const SizedBox(height: 8),
          // Dots indicator skeleton with staggered effect
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: List.generate(
              3,
              (index) => Container(
                width: 8,
                height: 8,
                margin: const EdgeInsets.symmetric(horizontal: 2),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Colors.grey.shade300,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
