import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rozana/features/cart/bloc/cart_bloc.dart';
import 'package:rozana/features/cart/bloc/cart_state.dart';
import 'package:rozana/features/cart/presentation/widgets/floating_cart_button.dart';

import '../../../location/bloc/location bloc/location_bloc.dart';
import '../../../order/presentation/widgets/order_tracking_wrapper.dart';

/// A wrapper widget that adds a floating cart button to any screen
///
/// This widget should be used to wrap screens where you want the floating cart button to appear.
/// It will automatically show/hide the button based on the cart state.
///
/// Example usage:
/// ```dart
/// FloatingCartWrapper(
///   child: YourScreen(),
/// )
/// ```
class FloatingCartWrapper extends StatelessWidget {
  final Widget child;

  /// Whether to exclude the floating cart button on this screen
  /// Set to true for screens like the cart screen where the button is not needed
  final bool excludeFloatingCart;
  final bool excludeOrderTile;

  /// Bottom padding to apply to the floating cart button
  /// Useful when there's a bottom navigation bar
  final double bottomPadding;

  const FloatingCartWrapper({
    super.key,
    required this.child,
    this.excludeFloatingCart = false,
    this.bottomPadding = 0,
    this.excludeOrderTile = false,
  });

  @override
  Widget build(BuildContext context) {
    if (excludeFloatingCart) {
      return child;
    }

    return Stack(
      children: [
        child,
        BlocBuilder<LocationBloc, LocationState>(
          builder: (context, state) {
            return state.maybeWhen(
              // When location is not serviceable, show the not serviceable content
              notServiceable: (address) => SizedBox.shrink(),
              // For all other states, show the normal app content
              orElse: () => BlocBuilder<CartBloc, CartState>(
                buildWhen: (previous, current) =>
                    previous.cart.totalItems != current.cart.totalItems,
                builder: (context, state) {
                  // Don't show the button if cart is empty
                  if (state.cart.totalItems == 0) {
                    return OrderTrackingWrapper(
                      excludeOrderTile: excludeFloatingCart || excludeOrderTile,
                      child: child,
                    );
                    // return const SizedBox.shrink();
                  }

                  return Positioned(
                    bottom: bottomPadding,
                    left: 0,
                    right: 0,
                    child: Center(
                      child: Padding(
                        padding: EdgeInsets.only(
                            bottom: MediaQuery.of(context).viewInsets.bottom),
                        child: const FloatingCartButton(),
                      ),
                    ),
                  );
                },
              ),
            );
          },
        ),
      ],
    );
  }
}
