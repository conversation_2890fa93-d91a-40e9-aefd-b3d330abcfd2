import 'package:appsflyer_sdk/appsflyer_sdk.dart';
import 'package:rozana/core/utils/logger.dart';

import 'app_flyer_deeplink.dart';

class AppsFlyerServices {
  static AppsFlyerOptions? appsFlyerOptions;
  static AppsflyerSdk? appsflyerSdk;
  static String? pendingLink;
  static Map<String, dynamic>? pendingLinkArguments;
  static Future<void> initAppsFlyer() async {
    // AppsFlyer
    appsFlyerOptions = AppsFlyerOptions(
      afDevKey: 'aZ7jXgHRGzxkBxwkSpp3FB',
      appId: "com.user.mobile.rozan", // Your app ID
      showDebug: true, // Display debug logs
      timeToWaitForATTUserAuthorization: 50, // Required for iOS 14.5+
    );
    appsflyerSdk = AppsflyerSdk(appsFlyerOptions);
    appsflyerSdk
        ?.initSdk(
      registerConversionDataCallback: true,
      registerOnAppOpenAttributionCallback: true,
      registerOnDeepLinkingCallback: true,
    )
        .then((value) async {
      appsflyerSdk?.onDeepLinking((DeepLinkResult result) {
        if (result.status == Status.FOUND) {
          AppFlyerDeeplink.handleDeepLink(result);
        }
      });
    });
  }

  static Future<void> pushUserProfile(String userId) async {
    appsflyerSdk?.setCustomerUserId(userId);
  }

  static Future<void> logEvent(String eventName, Map eventValues) async {
    bool result = false;
    try {
      result = await appsflyerSdk?.logEvent(eventName, {
            ...eventValues,
            'af_currency': 'INR',
          }) ??
          false;
    } on Exception catch (_) {
      // Handle exception
    }
    LogMessage.p("Result logEvent: $eventName $result");
  }
}
