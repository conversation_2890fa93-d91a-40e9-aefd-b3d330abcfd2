import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:rozana/core/config/environment_config.dart';

import 'package:rozana/core/services/app_preferences_service.dart';
import 'package:rozana/core/utils/logger.dart';
import 'package:rozana/core/services/app_verification_service.dart';
import 'package:rozana/core/services/app_initialization_service.dart';

import '../core/services/appflyer_services/app_flyers_services.dart';

/// Application configuration settings
class AppConfig {
  static Environment get environment => EnvironmentConfig.environment;

  static Future<void> init() async {
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
        statusBarBrightness: Brightness.light,
        systemNavigationBarColor: Colors.white,
        systemNavigationBarIconBrightness: Brightness.dark,
      ),
    );

    // Set preferred orientations
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
    await AppPreferences.init();

    // AppsFlyer sdk initialization
    await AppsFlyerServices.initAppsFlyer();

    try {
      // Initialize Firebase and Remote Config using the AppInitializationService
      // This ensures Firebase and Remote Config are initialized before Typesense
      final initService = AppInitializationService();
      await initService.initialize();
      // Validate environment configuration before proceeding
      EnvironmentConfig.validateConfiguration();
      LogMessage.p('App initialization completed successfully',
          color: Colors.green);

      // Configure Firebase Auth verification settings
      final verificationService = AppVerificationService();
      verificationService.configureFirebaseAuth();
    } catch (e) {
      LogMessage.p('App initialization failed: $e', color: Colors.red);
      // Don't rethrow if it's a duplicate app error, as the app can still function
      if (!e.toString().contains('duplicate-app')) {
        rethrow;
      }
    }
  }

  static const bool useAddressIdForAutoSelection =
      true; // Set to false for normal flow

  /// Get the appropriate API base URL based on environment
  static String get baseUrl =>
      EnvironmentConfig.omsBaseUrl; // Default to OMS for backward compatibility

  /// Get OMS (Order Management System) base URL
  static String get omsBaseUrl => EnvironmentConfig.omsBaseUrl;

  /// Get IMS (Inventory Management System) base URL
  static String get imsBaseUrl => EnvironmentConfig.imsBaseUrl;

  /// Get base URL for specific service
  /// [isIms] - true for IMS service, false for OMS service
  static String getServiceBaseUrl({required bool isIms}) {
    return EnvironmentConfig.getServiceBaseUrl(isIms: isIms);
  }
}
