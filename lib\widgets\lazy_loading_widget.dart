import 'package:flutter/material.dart';

/// A generic lazy loading widget that can be used to display data with lazy loading
class LazyLoadingWidget<T> extends StatefulWidget {
  /// The data to display
  final List<T> items;

  /// Whether the data is currently loading
  final bool isLoading;

  /// Function to call when the user scrolls to the end of the list
  final VoidCallback? onLoadMore;

  /// Function to build each item in the list
  final Widget Function(BuildContext context, T item, int index) itemBuilder;

  /// Function to build the loading indicator
  final Widget Function(BuildContext context)? loadingBuilder;

  /// Function to build the empty state
  final Widget Function(BuildContext context)? emptyBuilder;

  /// The scroll direction of the list
  final Axis scrollDirection;

  /// Whether the list should shrink wrap its contents
  final bool shrinkWrap;

  /// The physics of the scroll view
  final ScrollPhysics? physics;

  /// The scroll controller to use
  final ScrollController? scrollController;

  /// The padding to apply to the list
  final EdgeInsetsGeometry? padding;

  /// Whether there is more data to load
  final bool hasMoreData;

  /// The threshold percentage (0.0 to 1.0) to trigger loading more data
  final double loadMoreThreshold;

  /// The number of items to display per page
  final int? itemsPerPage;

  /// Whether to show the loading indicator at the bottom of the list
  final bool showLoadingIndicatorAtBottom;

  const LazyLoadingWidget({
    super.key,
    required this.items,
    required this.isLoading,
    required this.itemBuilder,
    this.onLoadMore,
    this.loadingBuilder,
    this.emptyBuilder,
    this.scrollDirection = Axis.vertical,
    this.shrinkWrap = false,
    this.physics,
    this.scrollController,
    this.padding,
    this.hasMoreData = false,
    this.loadMoreThreshold = 0.8,
    this.itemsPerPage,
    this.showLoadingIndicatorAtBottom = true,
  });

  @override
  State<LazyLoadingWidget<T>> createState() => _LazyLoadingWidgetState<T>();
}

class _LazyLoadingWidgetState<T> extends State<LazyLoadingWidget<T>> {
  late ScrollController _scrollController;
  bool _isLoadingMore = false;

  @override
  void initState() {
    super.initState();
    _scrollController = widget.scrollController ?? ScrollController();
    _setupScrollListener();
  }

  @override
  void dispose() {
    if (widget.scrollController == null) {
      _scrollController.dispose();
    }
    super.dispose();
  }

  void _setupScrollListener() {
    _scrollController.addListener(() {
      if (!_isLoadingMore &&
          widget.hasMoreData &&
          widget.onLoadMore != null &&
          _scrollController.position.pixels >=
              _scrollController.position.maxScrollExtent *
                  widget.loadMoreThreshold) {
        setState(() {
          _isLoadingMore = true;
        });
        widget.onLoadMore!();
        Future.delayed(const Duration(milliseconds: 500), () {
          if (mounted) {
            setState(() {
              _isLoadingMore = false;
            });
          }
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    // Show loading indicator only if loading and no items are available yet
    if (widget.isLoading && widget.items.isEmpty) {
      return widget.loadingBuilder?.call(context) ??
          const Center(child: CircularProgressIndicator());
    }

    // Show empty state if no items and not loading
    if (!widget.isLoading && widget.items.isEmpty) {
      return widget.emptyBuilder?.call(context) ??
          const Center(child: Text('No items available'));
    }

    // Build list with items
    return ListView.builder(
      controller: _scrollController,
      scrollDirection: widget.scrollDirection,
      shrinkWrap: widget.shrinkWrap,
      physics: widget.physics,
      padding: widget.padding,
      itemCount: widget.items.length +
          (widget.showLoadingIndicatorAtBottom &&
                  widget.hasMoreData &&
                  _isLoadingMore
              ? 1
              : 0),
      itemBuilder: (context, index) {
        // Show loading indicator at the end only when actively loading more data
        if (widget.showLoadingIndicatorAtBottom &&
            widget.hasMoreData &&
            _isLoadingMore &&
            index == widget.items.length) {
          return const Padding(
            padding: EdgeInsets.all(16.0),
            child: Center(
              child: SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
            ),
          );
        }

        // Build regular item
        return widget.itemBuilder(context, widget.items[index], index);
      },
    );
  }
}

/// A horizontal lazy loading list widget
class HorizontalLazyLoadingList<T> extends StatelessWidget {
  /// The data to display
  final List<T> items;

  /// Whether the data is currently loading
  final bool isLoading;

  /// Function to call when the user scrolls to the end of the list
  final VoidCallback? onLoadMore;

  /// Function to build each item in the list
  final Widget Function(BuildContext context, T item, int index) itemBuilder;

  /// The height of the list
  final double height;

  /// Whether there is more data to load
  final bool hasMoreData;

  /// The padding to apply to the list
  final EdgeInsetsGeometry? padding;

  /// The scroll controller to use
  final ScrollController? scrollController;

  final bool showEmptyText;

  const HorizontalLazyLoadingList({
    super.key,
    required this.items,
    required this.isLoading,
    required this.itemBuilder,
    required this.height,
    this.onLoadMore,
    this.hasMoreData = false,
    this.padding,
    this.scrollController,
    this.showEmptyText = true,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: items.isEmpty
          ? showEmptyText
              ? height
              : 0
          : height,
      child: LazyLoadingWidget<T>(
        items: items,
        isLoading: isLoading,
        itemBuilder: itemBuilder,
        onLoadMore: onLoadMore,
        hasMoreData: hasMoreData,
        scrollDirection: Axis.horizontal,
        padding: (items.isEmpty)
            ? showEmptyText
                ? (padding ?? const EdgeInsets.symmetric(horizontal: 16))
                : EdgeInsets.zero
            : (padding ?? const EdgeInsets.symmetric(horizontal: 16)),
        scrollController: scrollController,
        loadingBuilder: (context) =>
            const Center(child: CircularProgressIndicator()),
        emptyBuilder: (context) => showEmptyText
            ? const Center(child: Text('No items available'))
            : SizedBox(),
      ),
    );
  }
}

/// A grid lazy loading widget
class GridLazyLoadingWidget<T> extends StatefulWidget {
  /// The data to display
  final List<T> items;

  /// Whether the data is currently loading
  final bool isLoading;

  /// Function to call when the user scrolls to the end of the list
  final VoidCallback? onLoadMore;

  /// Function to build each item in the grid
  final Widget Function(BuildContext context, T item, int index) itemBuilder;

  /// The grid delegate to use
  final SliverGridDelegate gridDelegate;

  /// Whether there is more data to load
  final bool hasMoreData;

  /// Whether the grid should shrink wrap its contents
  final bool shrinkWrap;

  /// The physics of the scroll view
  final ScrollPhysics? physics;

  /// The padding to apply to the grid
  final EdgeInsetsGeometry? padding;

  /// The scroll controller to use
  final ScrollController? scrollController;
  final bool? primary;

  const GridLazyLoadingWidget({
    super.key,
    required this.items,
    required this.isLoading,
    required this.itemBuilder,
    required this.gridDelegate,
    this.onLoadMore,
    this.hasMoreData = false,
    this.shrinkWrap = true,
    this.physics = const NeverScrollableScrollPhysics(),
    this.padding,
    this.scrollController,
    this.primary,
  });

  @override
  State<GridLazyLoadingWidget<T>> createState() =>
      _GridLazyLoadingWidgetState<T>();
}

class _GridLazyLoadingWidgetState<T> extends State<GridLazyLoadingWidget<T>> {
  late ScrollController _scrollController;
  bool _isLoadingMore = false;

  @override
  void initState() {
    super.initState();
    _scrollController = widget.scrollController ?? ScrollController();
    _setupScrollListener();
  }

  @override
  void dispose() {
    if (widget.scrollController == null) {
      _scrollController.dispose();
    }
    super.dispose();
  }

  void _setupScrollListener() {
    _scrollController.addListener(() {
      if (!_isLoadingMore &&
          widget.hasMoreData &&
          widget.onLoadMore != null &&
          _scrollController.position.pixels >=
              _scrollController.position.maxScrollExtent * 0.8) {
        if (mounted) {
          setState(() {
            _isLoadingMore = true;
          });
        }

        widget.onLoadMore!();
        Future.delayed(const Duration(milliseconds: 500), () {
          if (mounted) {
            setState(() {
              _isLoadingMore = false;
            });
          }
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    if (widget.isLoading && widget.items.isEmpty) {
      return const SizedBox(
        height: 100,
        child: Center(child: CircularProgressIndicator()),
      );
    }

    if (!widget.isLoading && widget.items.isEmpty) {
      return const Center(child: Text('No items available'));
    }

    return GridView.builder(
      primary: widget.primary,
      gridDelegate: widget.gridDelegate,
      shrinkWrap: widget.shrinkWrap,
      physics: widget.physics,
      controller: _scrollController,
      padding: widget.padding,
      itemCount: widget.items.length + (_isLoadingMore ? 1 : 0),
      itemBuilder: (context, index) {
        if (_isLoadingMore && index == widget.items.length) {
          return const Center(
            child: SizedBox(
              width: 24,
              height: 24,
              child: CircularProgressIndicator(strokeWidth: 2),
            ),
          );
        }

        // Build regular item
        return widget.itemBuilder(context, widget.items[index], index);
      },
    );
  }
}
