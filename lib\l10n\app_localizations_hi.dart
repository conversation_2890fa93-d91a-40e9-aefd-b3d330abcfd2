// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Hindi (`hi`).
class AppLocalizationsHi extends AppLocalizations {
  AppLocalizationsHi([String locale = 'hi']) : super(locale);

  @override
  String get appTitle => 'रोज़ाना';

  @override
  String get myCart => 'मेरी कार्ट';

  @override
  String get placeOrder => 'ऑर्डर दें';

  @override
  String get selectAddress => 'पता चुनें';

  @override
  String get loginToProceed => 'आगे बढ़ने के लिए लॉगिन करें';

  @override
  String get myProfile => 'मेरी प्रोफाइल';

  @override
  String get yourOrders => 'आपके ऑर्डर';

  @override
  String get helpSupport => 'सहायता और समर्थन';

  @override
  String get logout => 'लॉगआउट';

  @override
  String get retry => 'पुनः प्रयास करें';

  @override
  String get loading => 'लोड हो रहा है...';

  @override
  String get error => 'त्रुटि';

  @override
  String get somethingWentWrong => 'कुछ गलत हुआ';

  @override
  String get welcomeToRozana => 'रोज़ाना में आपका स्वागत है';

  @override
  String get verifyOtp => 'OTP सत्यापित करें';

  @override
  String get total => 'कुल';

  @override
  String totalItems(int count) {
    return 'कुल ($count आइटम)';
  }

  @override
  String get savedAddresses => 'सहेजे गए पते';

  @override
  String get address => 'पता';

  @override
  String get addresses => 'पते';

  @override
  String get refunds => 'रिफंड';

  @override
  String get profile => 'प्रोफाइल';

  @override
  String get rewards => 'रिवार्ड्स';

  @override
  String get paymentManagement => 'भुगतान प्रबंधन';

  @override
  String get wishlist => 'विशलिस्ट';

  @override
  String get notifications => 'सूचनाएं';

  @override
  String get rozanaPay => 'रोज़ाना पे';

  @override
  String get language => 'भाषा';

  @override
  String get english => 'English';

  @override
  String get hindi => 'हिंदी';

  @override
  String get clearCart => 'कार्ट साफ़ करें';

  @override
  String get confirmClearCart => 'क्या आप वाकई अपनी कार्ट साफ़ करना चाहते हैं?';

  @override
  String get cancel => 'रद्द करें';

  @override
  String get clear => 'साफ़ करें';

  @override
  String get save => 'सहेजें';

  @override
  String get ok => 'ठीक है';

  @override
  String get yes => 'हाँ';

  @override
  String get no => 'नहीं';

  @override
  String get home => 'घर';

  @override
  String get work => 'कार्यालय';

  @override
  String get other => 'अन्य';

  @override
  String get addToCart => 'कार्ट में जोड़ें';

  @override
  String get buyNow => 'अभी खरीदें';

  @override
  String get outOfStock => 'स्टॉक में नहीं';

  @override
  String get login => 'लॉगिन';

  @override
  String get signUp => 'साइन अप';

  @override
  String get otpVerification => 'OTP सत्यापन';

  @override
  String get addAddress => 'पता जोड़ें';

  @override
  String get editAddress => 'पता संपादित करें';

  @override
  String get deleteAddress => 'पता हटाएं';

  @override
  String get defaultAddress => 'डिफ़ॉल्ट पता';

  @override
  String get setAsDefault => 'डिफ़ॉल्ट के रूप में सेट करें';

  @override
  String get deliveryAddress => 'डिलीवरी पता';

  @override
  String get paymentMethod => 'भुगतान विधि';

  @override
  String get cashOnDelivery => 'कैश ऑन डिलीवरी';

  @override
  String get orderSummary => 'ऑर्डर सारांश';

  @override
  String get subtotal => 'उप-योग';

  @override
  String get deliveryFee => 'डिलीवरी शुल्क';

  @override
  String get tax => 'कर';

  @override
  String get discount => 'छूट';

  @override
  String get free => 'मुफ़्त';

  @override
  String get youSaved => 'आपने बचाया';

  @override
  String get onThisOrder => 'इस ऑर्डर पर';

  @override
  String get emptyCart => 'आपकी कार्ट खाली है';

  @override
  String get startShopping => 'खरीदारी शुरू करें';

  @override
  String get remove => 'हटाएं';

  @override
  String get quantity => 'मात्रा';

  @override
  String get price => 'कीमत';

  @override
  String get originalPrice => 'मूल कीमत';

  @override
  String get discountedPrice => 'छूट वाली कीमत';

  @override
  String get refresh => 'रिफ्रेश';

  @override
  String get pullToRefresh => 'रिफ्रेश करने के लिए खींचें';

  @override
  String get deleteMyAccount => 'मेरा खाता हटाएं';

  @override
  String get aboutUs => 'हमारे बारे में';

  @override
  String get privacyPolicy => 'गोपनीयता नीति';

  @override
  String get termsAndConditions => 'नियम और शर्तें';
}
