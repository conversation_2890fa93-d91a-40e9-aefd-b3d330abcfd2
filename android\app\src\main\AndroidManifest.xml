<manifest xmlns:android="http://schemas.android.com/apk/res/android">
    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION"/>
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION"/>
    <application
        android:label="rozana"
        android:name="${applicationName}"
        android:icon="@mipmap/launcher_icon">
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:launchMode="singleTop"
            android:taskAffinity=""
            android:theme="@style/LaunchTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:windowSoftInputMode="adjustResize">
            <!-- Specifies an Android theme to apply to this Activity as soon as
                 the Android process has started. This theme is visible to the user
                 while the Flutter UI initializes. After that, this theme continues
                 to determine the Window background behind the Flutter UI. -->
            <meta-data
              android:name="io.flutter.embedding.android.NormalTheme"
              android:resource="@style/NormalTheme"
              />
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
            <!-- URI Scheme -->
            <intent-filter>
				<!-- If utilizing $deeplink_path please explicitly declare your hosts, or utilize a wildcard(*) -->
				<data android:scheme="rozanatest" android:host="open" />
				<action android:name="android.intent.action.VIEW" />
				<category android:name="android.intent.category.DEFAULT" />
				<category android:name="android.intent.category.BROWSABLE" />
			</intent-filter>

			<!-- App Links - Live App -->
			<intent-filter android:autoVerify="true">
				<action android:name="android.intent.action.VIEW" />
				<category android:name="android.intent.category.DEFAULT" />
				<category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="https" android:host="rozanatest.onelink.me" />
			</intent-filter>
        </activity>
        <!-- Don't delete the meta-data below.
             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java -->
        <meta-data
            android:name="flutterEmbedding"
            android:value="2" />
        <meta-data
            android:name="com.google.android.geo.API_KEY"
            android:value="AIzaSyCmmXv4dj0eAFJlhCPXjyQZ-Q0sXHcYFy4" />
        <!-- Razorpay Checkout Activity -->
        <activity
            android:name="com.razorpay.CheckoutActivity"
            android:exported="false"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
            android:theme="@style/CheckoutTheme">
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <data
                    android:host="rzp.io"
                    android:scheme="io.rzp"/>
            </intent-filter>
        </activity>

        <!-- Razorpay External Wallet Receiver -->
        <receiver
            android:name="com.razorpay.RzpTokenReceiver"
            android:exported="false">
            <intent-filter>
                <action android:name="rzp.device_token.share"/>
            </intent-filter>
        </receiver>
    </application>
    <!-- Required to query activities that can process text, see:
         https://developer.android.com/training/package-visibility and
         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.

         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin. -->
    <queries>
        <intent>
            <action android:name="android.intent.action.PROCESS_TEXT"/>
            <data android:mimeType="text/plain"/>
        </intent>
    </queries>
</manifest>
