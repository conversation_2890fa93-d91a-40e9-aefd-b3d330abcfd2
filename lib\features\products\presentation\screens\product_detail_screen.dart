import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:rozana/features/cart/bloc/cart_bloc.dart';
import 'package:rozana/features/cart/bloc/cart_state.dart';
import 'package:rozana/features/cart/presentation/screens/cart_screen.dart';
import 'package:rozana/routes/app_router.dart';

import '../../../../core/services/appflyer_services/app_flyer_deeplink.dart';
import '../../../../core/themes/color_schemes.dart';
import '../../../../data/models/cart_item_model.dart';
import '../../../cart/bloc/cart_event.dart';
import '../../../cart/utils/cart_utils.dart';
import '../widgets/product_section.dart';
import '../widgets/product_image_slider.dart';
import 'package:rozana/features/search/services/typesense_service.dart';
import 'package:get_it/get_it.dart';

class ProductDetailPage extends StatefulWidget {
  final Map<String, dynamic> productData;

  const ProductDetailPage({
    super.key,
    required this.productData,
  });

  @override
  State<ProductDetailPage> createState() => _ProductDetailPageState();
}

class _ProductDetailPageState extends State<ProductDetailPage> {
  @override
  void initState() {
    super.initState();
    getProductBySKU(widget.productData['sku']);
  }

  Future<void> getProductBySKU(String sku) async {
    // Implement the logic to fetch product details by SKU
    // This is just a placeholder for the actual implementation
    final typesenseService = GetIt.instance.get<TypesenseService>(); // Replace with your actual TypesenseService
    print("Fetching product details for SKU: $sku");
    final result = await typesenseService.getProductBySKU(sku: sku);
    debugPrint("Product details got from SKU: $result.toString()");
  }
  @override
  Widget build(BuildContext context) {
    final String name = widget.productData['name'] ?? '--';
    final String productId = widget.productData['id'] ?? '';
    final String sku = widget.productData['sku'] ?? '';
    final String description =
        widget.productData['description'] ?? 'No description available';
    final String imageUrl = (widget.productData['imageUrl'] is List)
        ? widget.productData['imageUrl'][0]
        : widget.productData['imageUrl'] ?? '';
    final List<String> photos = widget.productData['photos'] is List
        ? List<String>.from(widget.productData['photos'])
            .where((url) => url.isNotEmpty)
            .toList()
        : [imageUrl].where((url) => url.isNotEmpty).toList();
    final double price = widget.productData['price']?.toDouble() ?? 0.0;
    final double originalPrice =
        widget.productData['originalPrice']?.toDouble() ?? price;
    final double rating = widget.productData['rating']?.toDouble() ?? 0.0;
    final int reviewCount = widget.productData['reviewCount']?.toInt() ?? 0;
    final int discountPercentage =
        widget.productData['discountPercentage']?.toInt() ?? 0;
    // final bool isFeatured = widget.productData['isFeatured'] ?? false;
    final num availableQty = widget.productData['available_qty'];
    final num maxQty = widget.productData['max_purchase_limit'];
    final String? variantName =
        widget.productData['variant_name'] ?? widget.productData['variantName'];

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 1,
        foregroundColor: Colors.black,
        title: Text(name, style: const TextStyle(color: Colors.black)),
        actions: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 10),
            child: IconButton(
              icon: const Icon(Icons.share),
              onPressed: () => _createAndShareDeepLink(
                  productId, name, imageUrl, description, widget.productData),
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ProductImageSlider(
                    images: photos,
                    aspectRatio: 1,
                    showIndicators: true,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              name,
                              style: const TextStyle(
                                  fontSize: 22,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.black87),
                            ),
                            if (variantName != null &&
                                variantName.isNotEmpty) ...[
                              const SizedBox(height: 4),
                              Text(
                                variantName,
                                style: const TextStyle(
                                    fontSize: 14,
                                    color: Colors.grey,
                                    fontWeight: FontWeight.normal),
                              ),
                            ],
                          ],
                        ),
                      ),
                      if (discountPercentage > 0)
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: Colors.green[50],
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            '-$discountPercentage%',
                            style: const TextStyle(
                                color: Colors.green,
                                fontWeight: FontWeight.bold),
                          ),
                        ),
                    ],
                  ),
                  const SizedBox(height: 10),

                  Row(
                    children: [
                      Text(
                        '₹${price.round()}',
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Colors.green,
                        ),
                      ),
                      const SizedBox(width: 10),
                      if (originalPrice > price)
                        Text(
                          '₹${originalPrice.round()}',
                          style: const TextStyle(
                            fontSize: 16,
                            color: Colors.grey,
                            decoration: TextDecoration.lineThrough,
                          ),
                        ),
                      const SizedBox(
                        width: 10,
                      ),
                      if (originalPrice > price) ...[
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: Colors.blue,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            '${(100 - (price / originalPrice * 100)).round()}% off',
                            style: const TextStyle(
                              fontSize: 16,
                              color: AppColors.background,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ]
                    ],
                  ),
                  const SizedBox(height: 10),

                  // Ratings
                  Row(
                    children: [
                      ...List.generate(5, (index) {
                        if (index < rating.floor()) {
                          return const Icon(Icons.star,
                              size: 18, color: Colors.amber);
                        } else {
                          return const Icon(Icons.star_border,
                              size: 18, color: Colors.grey);
                        }
                      }),
                      const SizedBox(width: 8),
                      Text('$rating ($reviewCount reviews)',
                          style: const TextStyle(
                            fontSize: 14,
                            color: Colors.black26,
                          )),
                    ],
                  ),
                  const SizedBox(height: 20),
                  if (description.isNotEmpty) ...[
                    // Description
                    const Text(
                      "Product Description",
                      style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          color: Colors.black87),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      description,
                      style: const TextStyle(
                          fontSize: 15, height: 1.5, color: Colors.black54),
                    ),

                    const SizedBox(height: 32),
                  ],
                  // Similar Products Section
                  if (widget.productData['categoryId']?.toString().isNotEmpty ==
                      true)
                    ProductsSection.category(
                      categoryId: widget.productData['categoryId'].toString(),
                      title: 'View Similar',
                      height: 200,
                      showSeeAll:
                          false, // Don't show see all for similar products
                      excludeProductId: widget.productData['id']?.toString(),
                    ),
                  const SizedBox(height: 32),
                  if (widget.productData['brandId']?.toString().isNotEmpty ==
                      true)
                    ProductsSection.brand(
                      brandId: widget.productData['brandId'].toString(),
                      title: 'More from ${widget.productData['brandName']}',
                      height: 200,
                      showSeeAll:
                          false, // Don't show see all for similar products
                      excludeProductId: widget.productData['id']?.toString(),
                    ),
                ],
              ),
            ),
          ),

          // Bottom Buttons
          SafeArea(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
              child: BlocBuilder<CartBloc, CartState>(
                builder: (context, state) {
                  final quantity =
                      CartUtils.getItemQuantity(productId, sku, state.cart);
                  final totalItemsInCart = state.cart.totalItems;

                  return Row(
                    children: [
                      // View Cart button - only show if there are other items in cart
                      if (totalItemsInCart > 0) ...[
                        Expanded(
                          child: GestureDetector(
                            onTap: () {
                              HapticFeedback.lightImpact();
                              Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) => CartScreen()));
                            },
                            child: Container(
                              height: 48,
                              decoration: BoxDecoration(
                                color: Colors.transparent,
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(
                                  color: AppColors.primary,
                                  width: 1,
                                ),
                              ),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  // Cart icon with item count badge - exact same as floating cart button
                                  Stack(
                                    clipBehavior: Clip.none,
                                    children: [
                                      const Icon(
                                        Icons.shopping_cart_outlined,
                                        color: AppColors.primary,
                                        size: 22,
                                      ),
                                      Positioned(
                                        top: -8,
                                        right: -8,
                                        child: Container(
                                          padding: const EdgeInsets.all(4),
                                          decoration: const BoxDecoration(
                                            color: AppColors.primary,
                                            shape: BoxShape.circle,
                                          ),
                                          child: Text(
                                            '${state.cart.totalItems}',
                                            style: const TextStyle(
                                              color: Colors.white,
                                              fontSize: 10,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(width: 12),
                                  const Text(
                                    "View Cart",
                                    style: TextStyle(
                                      color: AppColors.primary,
                                      fontSize: 14,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                      ],

                      // Add to Cart / Quantity Selector
                      Expanded(
                        child: quantity > 0
                            ? _buildQuantitySelector(
                                productId,
                                sku,
                                name,
                                imageUrl,
                                originalPrice,
                                price,
                                availableQty,
                                maxQty)
                            : GestureDetector(
                                onTap: () {
                                  HapticFeedback.lightImpact();

                                  context.read<CartBloc>().add(
                                      CartEvent.addItem(
                                          item: CartItemModel(
                                              productId: productId,
                                              name: name,
                                              price: originalPrice,
                                              imageUrl: imageUrl,
                                              quantity: 1,
                                              unit:
                                                  'item', // Default unit, can be customized
                                              discountedPrice: price,
                                              facilityId: widget
                                                  .productData['facilityId']
                                                  ?.toString(),
                                              facilityName: widget
                                                  .productData['facilityName']
                                                  ?.toString(),
                                              skuID: widget.productData['sku']
                                                  ?.toString(),
                                              availableQuantity: availableQty,
                                              maxQuantity: maxQty)));
                                },
                                child: Container(
                                  height: 48,
                                  decoration: BoxDecoration(
                                    color: Colors.transparent,
                                    borderRadius: BorderRadius.circular(8),
                                    border: Border.all(
                                      color: AppColors.primary,
                                      width: 1,
                                    ),
                                  ),
                                  alignment: Alignment.center,
                                  child: const Text(
                                    "Add to Cart",
                                    style: TextStyle(
                                        color: AppColors.primary,
                                        fontSize: 14,
                                        fontWeight: FontWeight.bold),
                                  ),
                                ),
                              ),
                      ),
                    ],
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuantitySelector(
      String productId,
      String sku,
      String name,
      String imageUrl,
      double originalPrice,
      double discountedPrice,
      num availableQty,
      num maxQty) {
    return BlocBuilder<CartBloc, CartState>(
      builder: (context, state) {
        // final quantity = CartUtils.getItemQuantity(productId, state.cart);
        final quantity = CartUtils.getItemQuantity(productId, sku, state.cart);

        return Container(
          height: 48,
          decoration: BoxDecoration(
            color: Colors.transparent,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: AppColors.primary,
              width: 1,
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Minus button
              InkWell(
                splashColor: Colors.transparent,
                onTap: () async {
                  HapticFeedback.lightImpact();
                  final cartItemId =
                      CartUtils.getCartItemId(productId, sku, state.cart);

                  if (cartItemId != null) {
                    if (quantity > 1) {
                      context.read<CartBloc>().add(CartEvent.updateQuantity(
                          cartItemId, sku, (quantity - 1).toInt()));
                    } else if (quantity == 1) {
                      context
                          .read<CartBloc>()
                          .add(CartEvent.removeItem(cartItemId, sku));
                    }
                    // Force rebuild after async operation
                    if (mounted) {
                      setState(() {});
                    }
                  }
                },
                borderRadius:
                    const BorderRadius.horizontal(left: Radius.circular(8)),
                child: Container(
                  width: 40,
                  height: 32,
                  alignment: Alignment.center,
                  child: const Icon(
                    Icons.remove,
                    color: AppColors.primary,
                    size: 16,
                  ),
                ),
              ),

              // Quantity display
              Container(
                alignment: Alignment.center,
                width: 28,
                child: Text(
                  quantity.toString(),
                  style: const TextStyle(
                    color: AppColors.primary,
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                ),
              ),

              // Plus button
              InkWell(
                splashColor: Colors.transparent,
                onTap: () async {
                  HapticFeedback.lightImpact();
                  if (quantity == 0) {
                    context.read<CartBloc>().add(CartEvent.addItem(
                        item: CartItemModel(
                            productId: productId,
                            name: name,
                            price: originalPrice,
                            imageUrl: imageUrl,
                            quantity: 1,
                            unit: 'item', // Default unit, can be customized
                            discountedPrice: discountedPrice,
                            facilityId:
                                widget.productData['facilityId']?.toString(),
                            facilityName:
                                widget.productData['facilityName']?.toString(),
                            skuID: widget.productData['sku']?.toString(),
                            availableQuantity: availableQty,
                            maxQuantity: maxQty)));
                  } else {
                    final cartItemId =
                        CartUtils.getCartItemId(productId, sku, state.cart);
                    if (cartItemId != null) {
                      context.read<CartBloc>().add(CartEvent.updateQuantity(
                          cartItemId, sku, (quantity + 1).toInt()));
                    }
                  }
                  // widget.onAddToCart?.call();
                  // Force rebuild after async operation
                  if (mounted) {
                    setState(() {});
                  }
                },
                borderRadius:
                    const BorderRadius.horizontal(right: Radius.circular(8)),
                child: Container(
                  width: 40,
                  height: 32,
                  alignment: Alignment.center,
                  child: const Icon(
                    Icons.add,
                    color: AppColors.primary,
                    size: 16,
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}

/// Generates a Branch deep link and opens the native share sheet.
Future<void> _createAndShareDeepLink(
    String productId,
    String name,
    String imageUrl,
    String description,
    Map<String, dynamic> productData) async {
  AppFlyerDeeplink.createDeepLink(
    data: {
      'screen': RouteNames.productDetail,
      'product': jsonEncode(productData)
    },
    name: name,
  );
}
