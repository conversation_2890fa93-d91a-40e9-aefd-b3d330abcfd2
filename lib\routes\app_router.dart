import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:rozana/domain/entities/category_entity.dart';
import 'package:rozana/features/categories/bloc/categories_bloc.dart';
import 'package:rozana/features/home/<USER>/home%20bloc/home_bloc.dart';
import 'package:rozana/features/home/<USER>/screens/dashboard_screen.dart';
import 'package:rozana/features/location/presentation/screens/address_form_screen.dart';
import 'package:rozana/features/location/presentation/screens/map_screen.dart';
import 'package:rozana/features/products/bloc/product_listing_event.dart';
import 'package:rozana/features/products/presentation/screens/product_listing_screen.dart';

import '../core/connectivity/presentation/no_network_screen.dart';
import '../core/dependency_injection/di_container.dart';
import '../data/models/adress_model.dart';
import '../features/auth/bloc/login_bloc/login_bloc.dart';
import '../features/cart/presentation/screens/cart_screen.dart';
import '../features/categories/presentation/screens/categories_screen.dart';
import '../features/location/presentation/screens/address_list_screen.dart';
import '../features/location/presentation/screens/location_detection_screen.dart';
import '../features/products/bloc/product_listing_bloc.dart';
import '../features/products/presentation/screens/product_detail_screen.dart';
import '../features/products/presentation/screens/full_screen_image_viewer.dart';
import '../features/profile/bloc/profile_bloc.dart';
import '../features/profile/presentation/screen/profile_screen.dart';

import '../features/search/bloc/bloc/search_bloc.dart';
import '../features/search/presentation/screens/search_screen.dart';
import '../features/splash/presentation/screens/splash_screen.dart';
import '../features/auth/presentation/views/login_screen.dart';
import '../features/home/<USER>/screens/home_screen.dart';
import '../features/cart/presentation/screens/order_success_screen.dart';
import '../features/order/presentation/screens/order_history_screen.dart';
import '../features/order/presentation/screens/order_details_screen.dart';

import '../features/order/bloc/order_bloc.dart';
import '../app/bloc/app_bloc.dart';
import '../features/legal/presentation/screens/legal_content_screen.dart';
import '../features/legal/services/legal_content_service.dart';

class RouteNames {
  // Auth routes
  static const String splash = '/';
  static const String onboarding = '/onboarding';
  static const String login = '/login';
  static const String register = '/register';
  static const String forgotPassword = '/forgot-password';
  static const String verifyOtp = '/verify-otp';
  static const String resetPassword = '/reset-password';

  // Main routes
  static const String home = '/home';
  static const String categories = '/categories';
  static const String products = '/products';
  static const String productDetail = '/product-detail';
  static const String fullScreenImageViewer = '/full-screen-image-viewer';
  static const String search = '/search';
  static const String cart = '/cart';
  static const String checkout = '/checkout';
  static const String orderSuccess = '/order-success';
  static const String orders = '/orders';
  static const String orderDetail = '/order-detail';

  // User profile routes
  static const String profile = '/profile';
  static const String editProfile = '/edit-profile';
  static const String addresses = '/addresses';
  static const String addAddress = '/add-address';
  static const String editAddress = '/edit-address';
  static const String detectLocation = '/address/detect';
  static const String wishlist = '/wishlist';
  static const String mapForNewAddress = '/map-screen';
  static const String mapForEditAddress = '/map-screen/edit';

  // Settings and support routes
  static const String settings = '/settings';
  static const String notifications = '/notifications';
  static const String support = '/support';
  static const String faq = '/faq';
  static const String about = '/about';
  static const String privacyPolicy = '/privacy-policy';
  static const String termsConditions = '/terms-conditions';

  static const String noNetworkScreen = '/no-network';
}

class AppRouter {
  static GoRouter createRouter(BuildContext context) {
    return GoRouter(
      navigatorKey: navigatorKey,
      initialLocation: RouteNames.splash, // Start at splash screen
      // Handle Firebase Authentication deep links
      onException: (_, GoRouterState state, GoRouter router) {
        // If we get an exception with a path containing 'app-', it's likely a Firebase Auth deep link
        if (state.uri.toString().contains('app-')) {
          // Just ignore it and stay on the current page
          return;
        }
        // For other exceptions, navigate to splash as fallback
        router.go(RouteNames.splash);
      },
      // Use BlocListener for redirection based on authentication state
      redirect: (BuildContext context, GoRouterState state) {
        final AppState splashState = context.read<AppBloc>().state;

        // Handle Firebase Authentication deep links
        final String path = state.uri.toString();
        if (path.contains('app-1-211080965257')) {
          // This is a Firebase Auth deep link - don't redirect
          return null;
        }

        if (path.contains('rozanatest')) {
          return RouteNames.home; // Prevent navigation for shared links
        }

        final redirectLocation = splashState.maybeMap(
          loaded: (value) {
            if (value.isAuthenticated) {
              // If the user is authenticated and trying to go to login/splash, redirect to home
              if (state.uri.toString() == RouteNames.login ||
                  state.uri.toString() == RouteNames.splash) {
                return RouteNames.home;
              }
            } else {
              if (state.uri.toString().contains('/address')) {
                return RouteNames.mapForNewAddress;
              } else
              // If the user is not authenticated and trying to go to protected routes, redirect to login
              if (state.uri.toString() == RouteNames.profile ||
                  state.uri.toString() == RouteNames.orders ||
                  state.uri.toString().contains('/address')) {
                return RouteNames.login;
              }
            }
            return null;
          },
          orElse: () => null,
        );

        return redirectLocation;
      },
      routes: [
        GoRoute(
          path: RouteNames.splash,
          builder: (context, state) => const SplashScreen(),
        ),
        GoRoute(
          path: RouteNames.login,
          builder: (context, state) => BlocProvider(
            create: (context) => getIt<LoginBloc>()
              ..add(const LoginEvent.initLogin()), // Create LoginBloc here
            child: const LoginScreen(),
          ),
        ),
        ShellRoute(
          builder: (context, state, child) {
            return MultiBlocProvider(
              providers: [
                BlocProvider(
                    create: (context) =>
                        getIt<HomeBloc>()..add(HomeEvent.init())),
                BlocProvider(
                  create: (context) => getIt<CategoriesBloc>()
                    ..add(CategoriesEvent.fetchCategories()),
                ),
              ],
              child: HomeScreen(child: child),
            ); // Scaffold with BottomNav
          },
          routes: [
            GoRoute(
              path: RouteNames.home,
              pageBuilder: (context, state) =>
                  NoTransitionPage(child: const DashboardScreen()),
            ),
            GoRoute(
                path: RouteNames.categories,
                pageBuilder: (context, state) {
                  Map<dynamic, dynamic> extras =
                      (state.extra ?? {}) as Map<dynamic, dynamic>;
                  final CategoryEntity? category = extras['category'];
                  return NoTransitionPage(
                      child: CategoriesScreen(
                    scrollItem: category,
                  ));
                }),
            GoRoute(
              path: RouteNames.cart,
              pageBuilder: (context, state) =>
                  NoTransitionPage(child: CartScreen()),
            ),
            GoRoute(
              path: RouteNames.wishlist,
              pageBuilder: (context, state) =>
                  NoTransitionPage(child: SizedBox()),
            ),

            GoRoute(
              path: RouteNames.orders,
              builder: (context, state) {
                return BlocProvider(
                  create: (context) => getIt<OrderBloc>(),
                  child: const OrderHistoryScreen(),
                );
              },
            ),
            // Profile setup is now handled via modal, no route needed
          ],
        ),
        // Profile route - standalone without bottom navigation
        GoRoute(
          path: RouteNames.profile,
          builder: (context, state) => BlocProvider(
            create: (context) =>
                getIt<ProfileBloc>()..add(const ProfileEvent.loadUserData()),
            child: ProfileScreen(),
          ),
        ),
        GoRoute(
            path: RouteNames.products,
            builder: (context, state) {
              Map<String, dynamic> extras = state.extra as Map<String, dynamic>;
              final CategoryEntity? category = extras['category'];
              final CategoryEntity? parentCategory = extras['parent-category'];
              final CategoryEntity? subCategory = extras['sub_category'];

              return MultiBlocProvider(
                providers: [
                  BlocProvider(
                    create: (context) => getIt<ProductListingBloc>()
                      ..add(ProductListingEvent.initial(
                        category: category,
                      )),
                  ),
                  BlocProvider.value(
                      value: getIt<HomeBloc>()..add(HomeEvent.init()))
                ],
                child: ProductListingScreen(
                  category: category,
                  selectedSubcategory: subCategory,
                  parentCategory: parentCategory,
                ),
              );
            }),
        GoRoute(
            path: RouteNames.productDetail,
            builder: (context, state) {
              Map<String, dynamic> extras = state.extra as Map<String, dynamic>;
              return ProductDetailPage(
                productData: extras['product'],
              );
            }),
        GoRoute(
            path: RouteNames.fullScreenImageViewer,
            builder: (context, state) {
              Map<String, dynamic> extras = state.extra as Map<String, dynamic>;
              return FullScreenImageViewer(
                images: List<String>.from(extras['images']),
                initialIndex: extras['initialIndex'] ?? 0,
              );
            }),
        GoRoute(
          path: RouteNames.search,
          builder: (context, state) {
            String? query = state.uri.queryParameters['q'];

            return BlocProvider(
              create: (context) =>
                  getIt<SearchBloc>()..add(SearchEvent.init(query ?? '')),
              child: SearchScreen(),
            );
          },
        ),
        GoRoute(
          path: RouteNames.orderSuccess,
          builder: (context, state) {
            final Map<String, dynamic> args =
                state.extra as Map<String, dynamic>;
            return OrderSuccessScreen(
              orderId: args['orderId'],
              orderData: args['orderData'],
            );
          },
        ),

        GoRoute(
          path: '${RouteNames.orders}/:orderId',
          builder: (context, state) {
            final orderId = state.pathParameters['orderId']!;
            final fromOrderSuccess = state.extra != null &&
                state.extra is Map<String, dynamic> &&
                (state.extra as Map<String, dynamic>)['fromOrderSuccess'] ==
                    true;

            return BlocProvider(
              create: (context) => getIt<OrderBloc>(),
              child: OrderDetailsScreen(
                orderId: orderId,
                fromOrderSuccess: fromOrderSuccess,
              ),
            );
          },
        ),

        GoRoute(
            path: RouteNames.addresses,
            builder: (context, state) {
              Map<String, dynamic> extras = state.extra != null
                  ? state.extra as Map<String, dynamic>
                  : {};
              final selectMode = extras['selectMode'] as bool? ?? false;
              final allowEdit = extras['allowEdit'] as bool? ?? false;
              final onAddressSelected =
                  extras['onAddressSelected'] as Function(AddressModel)?;
              return AddressListScreen(
                selectMode: selectMode,
                allowEdit: allowEdit,
                onAddressSelected: onAddressSelected,
              );
            }),
        GoRoute(
            path: RouteNames.editAddress,
            builder: (context, state) {
              final extras = state.extra;
              if (extras is AddressModel) {
                // Editing existing address
                return AddressFormScreen(address: state.extra as AddressModel);
              } else if (extras is Map<String, dynamic>) {
                // Editing address with extras
                final addressModel = extras['address'] as AddressModel;
                final fromCart = extras['fromCart'] as bool? ?? false;

                return AddressFormScreen(
                  address: addressModel,
                  fromCart: fromCart,
                );
              } else {
                // No data provided, create new address
                return const AddressFormScreen();
              }
            }),
        GoRoute(
            path: RouteNames.detectLocation,
            builder: (context, state) {
              Map<String, dynamic> extras = state.extra != null
                  ? state.extra as Map<String, dynamic>
                  : {};
              final onAddressSelected =
                  extras['onAddressSelected'] as Function(AddressModel?)?;
              final showSkip = extras['showSkip'] as bool? ?? true;
              return LocationDetectionScreen(
                onAddressSelected: onAddressSelected ?? (_) {},
                showSkip: showSkip,
              );
            }),
        // Specific route for the Firebase Authentication deep link that's causing the issue
        GoRoute(
          path: 'app-1-211080965257',
          builder: (context, state) {
            // Just show a loading indicator and let the auth flow continue
            return const Scaffold(
              body: Center(
                child: CircularProgressIndicator(),
              ),
            );
          },
        ),
        // Additional catch-all routes with different patterns
        GoRoute(
          path: '/app-1-211080965257',
          builder: (context, state) {
            return const Scaffold(
              body: Center(
                child: CircularProgressIndicator(),
              ),
            );
          },
        ),
        // Generic catch-all for any other app- patterns
        GoRoute(
          path: 'app-:appId',
          builder: (context, state) {
            return const Scaffold(
              body: Center(
                child: CircularProgressIndicator(),
              ),
            );
          },
        ),
        GoRoute(
            path: RouteNames.mapForNewAddress,
            builder: (context, state) {
              Map<String, dynamic> extras = state.extra != null
                  ? state.extra as Map<String, dynamic>
                  : {};
              final fromCart = extras['fromCart'] as bool? ?? false;
              return MapScreen(fromCart: fromCart);
            }),
        GoRoute(
            path: RouteNames.mapForEditAddress,
            builder: (context, state) {
              if (state.extra is AddressModel) {
                // Editing existing address
                return MapScreen(address: state.extra as AddressModel);
              } else {
                return MapScreen();
              }
            }),
        // Legal content routes
        GoRoute(
          path: RouteNames.about,
          builder: (context, state) => LegalContentScreen(
            contentType: LegalContentType.aboutUs,
          ),
        ),
        GoRoute(
          path: RouteNames.privacyPolicy,
          builder: (context, state) => LegalContentScreen(
            contentType: LegalContentType.privacyPolicy,
          ),
        ),
        GoRoute(
          path: RouteNames.termsConditions,
          builder: (context, state) => LegalContentScreen(
            contentType: LegalContentType.termsConditions,
          ),
        ),
        GoRoute(
          path: RouteNames.noNetworkScreen,
          builder: (context, state) => NoNetworkScreen(),
        ),
      ],
    );
  }
}
