import 'package:flutter/material.dart';
import 'package:razorpay_flutter/razorpay_flutter.dart';
import '../../../core/services/remote_config_service.dart';

class RazorpayService {
  static final RemoteConfigService _remoteConfig = RemoteConfigService();
  static final String _keyId = _remoteConfig.razorpayKey;

  late Razorpay _razorpay;

  // Callbacks
  final Function(PaymentSuccessResponse) onPaymentSuccess;
  final Function(PaymentFailureResponse) onPaymentError;
  final Function(ExternalWalletResponse) onExternalWallet;

  RazorpayService({
    required this.onPaymentSuccess,
    required this.onPaymentError,
    required this.onExternalWallet,
  }) {
    _initRazorpay();
  }

  void _initRazorpay() {
    _razorpay = Razorpay();
    _razorpay.on(Razorpay.EVENT_PAYMENT_SUCCESS, onPaymentSuccess);
    _razorpay.on(Razorpay.EVENT_PAYMENT_ERROR, onPaymentError);
    _razorpay.on(Razorpay.EVENT_EXTERNAL_WALLET, onExternalWallet);
  }

  void processPayment({
    required String razorpayOrderId,
    required String name,
    required String email,
    required String contact,
    String? description,
  }) {
    var options = {
      'key': _keyId,
      'order_id': razorpayOrderId, // Razorpay order ID contains amount, currency, etc.
      'name': 'Rozana',
      'description': description ?? 'Payment for your order',
      'prefill': {'contact': contact, 'email': email, 'name': name},
      'theme': {
        'color': '#3F51B5' // Match with app's primary color
      }
    };

    try {
      _razorpay.open(options);
    } catch (e) {
      debugPrint('Error: $e');
    }
  }

  void dispose() {
    _razorpay.clear();
  }
}
