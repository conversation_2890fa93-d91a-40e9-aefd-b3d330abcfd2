import '../entities/product_entity.dart';
import '../../../../data/services/data_loading_manager.dart';

/// Use case for getting products
/// Encapsulates the business logic for retrieving products
class GetProductsUseCase {
  final DataLoadingManager _dataLoadingManager;

  const GetProductsUseCase(this._dataLoadingManager);

  /// Execute the use case to get products
  ///
  /// [page] - Page number for pagination (0-based)
  /// [pageSize] - Number of items per page
  /// [refresh] - Whether to refresh the data
  /// [query] - Search query filter
  /// [categoryId] - Optional category ID to filter products by category
  /// [brandId] - Optional brand ID to filter products by brand
  /// [sectionType] - Section type for dynamic products
  /// [dynamic] - Whether to use dynamic products loading
  /// [excludeProductId] - Optional product ID to exclude from results
  ///
  /// Returns a list of [ProductEntity]
  Future<List<ProductEntity>> execute({
    int page = 0,
    int pageSize = 9,
    bool refresh = false,
    String query = '',
    String? categoryId,
    String? brandId,
    String? sectionType,
    bool dynamic = false,
    String? excludeProductId,
  }) async {
    // Business logic can be added here
    // For example: validation, caching, filtering, etc.

    // Validate inputs
    if (page < 0) {
      throw ArgumentError('Page number cannot be negative');
    }

    if (pageSize <= 0) {
      throw ArgumentError('Page size must be greater than 0');
    }

    // Validate categoryId if provided
    if (categoryId != null && categoryId.isEmpty) {
      throw ArgumentError('Category ID cannot be empty');
    }

    // Validate brandId if provided
    if (brandId != null && brandId.isEmpty) {
      throw ArgumentError('Brand ID cannot be empty');
    }

    // Handle products by category
    if (categoryId != null) {
      return await _dataLoadingManager.loadProductsByCategory(
        categoryId: categoryId,
        page: page,
        pageSize: pageSize,
        refresh: refresh,
        query: query.isEmpty ? '*' : query,
        excludeProductId: excludeProductId,
      );
    }

    // Handle products by brand
    if (brandId != null) {
      return await _dataLoadingManager.loadProductsByBrand(
        brandId: brandId,
        page: page,
        pageSize: pageSize,
        refresh: refresh,
        query: query.isEmpty ? '*' : query,
        excludeProductId: excludeProductId,
      );
    }

    if (dynamic) {
      return await _dataLoadingManager.loadDynamicProducts(
        page: page,
        pageSize: pageSize,
        refresh: refresh,
        query: query,
        sectionType: sectionType ?? '',
      );
    }

    // Call repository to get data
    final products = await _dataLoadingManager.loadProducts(
      page: page,
      pageSize: pageSize,
      refresh: refresh,
      query: query,
      sectionType: sectionType ?? '',
    );

    // Apply business rules
    // For example: filter out out-of-stock products for certain queries
    if (query.toLowerCase() == 'available') {
      return products.where((product) => !product.isOutOfStock).toList();
    }

    // Sort by rating for 'popular' query
    if (query.toLowerCase() == 'popular') {
      final sortedProducts = List<ProductEntity>.from(products);
      sortedProducts.sort((a, b) => b.rating.compareTo(a.rating));
      return sortedProducts;
    }

    return products;
  }



}
